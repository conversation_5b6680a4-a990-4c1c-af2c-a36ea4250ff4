import { useAuthStore } from "@/store/auth";
import axios from "axios";
const API_URL = "https://present-backend-no5j.onrender.com/api/v1";
// export const LOCAL_URL = "http://192.168.1.160:5400/api/v1";

const api = axios.create({
  baseURL: API_URL,
});

api.interceptors.request.use((config) => {
  const token = useAuthStore.getState().user?.token;
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

export default api;
