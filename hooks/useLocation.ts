import * as Location from "expo-location";
import { useEffect, useState } from "react";
import Toast from "react-native-toast-message";

export function useLocation() {
  const [location, setLocation] = useState<Location.LocationObject | null>(
    null
  );
  const [locationLoading, setLocationLoading] = useState(true);

  useEffect(() => {
    (async () => {
      try {
        const { status } = await Location.requestForegroundPermissionsAsync();
        if (status !== "granted") {
          Toast.show({
            type: "error",
            text1: "Permission denied",
            text2: "Location permission is required",
          });
          setLocationLoading(false);
          return;
        }

        const currentLocation = await Location.getCurrentPositionAsync({
          accuracy: Location.Accuracy.Balanced,
        });
        setLocation(currentLocation);
      } catch (error) {
        Toast.show({
          type: "error",
          text1: "Location Error",
          text2: "Failed to get current location",
        });
      } finally {
        setLocationLoading(false);
      }
    })();
  }, []);
  return { location, locationLoading };
}
