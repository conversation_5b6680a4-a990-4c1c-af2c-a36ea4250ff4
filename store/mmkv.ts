import { MMKV } from "react-native-mmkv";
import { StateStorage } from "zustand/middleware/persist";

const storage = new MMKV({
  id: "presentt",
});

export const zustandStorage: StateStorage = {
  setItem: (name: string, value: string) => {
    return storage.set(name, value);
  },
  getItem: (name: string) => {
    const value = storage.getString(name);
    return value ?? null;
  },
  removeItem: (name: string) => {
    return storage.delete(name);
  },
};

export default storage;
