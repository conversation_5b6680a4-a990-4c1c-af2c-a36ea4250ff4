import { create } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";
// import { zustandStorage } from "./mmkv";
import AsyncStorage from "@react-native-async-storage/async-storage";

export interface User {
  id: string;
  email: string;
  verified: boolean;
  role: string;
  name: string;
  token: string;
}
interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  rehydrated: boolean;

  login: (user: User) => void;
  logout: () => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      isAuthenticated: false,
      user: null,
      rehydrated: false,
      login: (user) => set({ isAuthenticated: true, user }),
      logout: () => set({ isAuthenticated: false, user: null }),
    }),
    {
      name: "auth",
      storage: createJSONStorage(() => AsyncStorage),
      onRehydrateStorage: () => () => {
        // Called after rehydration completes
        useAuthStore.setState({ rehydrated: true });
      },
    }
  )
);
