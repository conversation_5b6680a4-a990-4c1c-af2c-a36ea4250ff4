import ArrowButton from "@/components/arrow-button";
import { useAuthStore } from "@/store/auth";
import { colors } from "@/utils/colors";
import { fonts } from "@/utils/fonts";
import { router } from "expo-router";
import { StyleSheet, Text, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
const Index = () => {
  const { user } = useAuthStore();
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <View>
          <Text style={styles.title}>Welcome! {user?.name}</Text>
        </View>
        <Text style={styles.subtitle}>What would you like to do today?</Text>
      </View>
      <View style={{ gap: 16 }}>
        <ArrowButton
          title="Create Session"
          onPress={() => router.push("/session/new-session")}
        />

        <ArrowButton
          title="Join Organisation"
          onPress={() => router.push("/join-org")}
        />

        <ArrowButton
          title="Continue"
          onPress={() => router.push("/(main)/home")}
        />
      </View>
    </SafeAreaView>
  );
};
export default Index;
const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
    backgroundColor: colors.background,
    justifyContent: "center",
    paddingVertical: 16,
  },
  header: {
    marginBottom: 32,
  },
  title: {
    fontSize: 30,
    fontFamily: fonts.bold,
    color: colors.secondary,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: "pt_regular",
    color: colors.text,
  },
});
