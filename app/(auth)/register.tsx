import { Button, Input } from "@/components";
import FormLayout from "@/components/form-layout";
import HeaderBack from "@/components/header-back";
import api from "@/lib/axios";
import { colors } from "@/utils/colors";
import { useMutation } from "@tanstack/react-query";
import { router } from "expo-router";
import { Formik } from "formik";
import { KeyboardAvoidingView, Platform, StyleSheet } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import * as yup from "yup";

const schema = yup.object().shape({
  name: yup.string().required(),
  email: yup.string().email().required(),
  password: yup.string().min(6).required(),
  confirmPassword: yup.string().min(6).required(),
});

type RegisterFormValues = {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
};

export default function Register() {
  const mutation = useMutation({
    mutationFn: (values: any) => api.post("/auth/register", values),
    onSuccess: (data: any) => {
      if (data.user.verified) router.push("/(main)/home");
      else router.push("/(auth)/verify");
    },
    onError: (error: any) => {
      console.log(error);
    },
  });
  const hadleSubmit = (values: RegisterFormValues) => {
    mutation.mutate({
      username: values.name,
      email: values.email,
      password: values.password,
      role: "user",
    });
  };
  return (
    <SafeAreaView style={styles.container}>
      <HeaderBack />
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.keyboardView}
      >
        <Formik
          initialValues={{
            name: "",
            email: "",
            password: "",
            confirmPassword: "",
          }}
          validationSchema={schema}
          onSubmit={hadleSubmit}
        >
          {({ handleChange, handleBlur, handleSubmit, values, errors }) => (
            <FormLayout
              title="Create Account"
              subtitle="
            Create new account to get started and enjoy seemless access to our features"
              footerLink={"/(auth)/login"}
              footerLinkText="Login"
              footerText="Already have an ccount?"
            >
              <>
                <Input
                  placeholder="Enter your name"
                  icon="person"
                  value={values.name}
                  onChangeText={handleChange("name")}
                  onBlur={() => handleBlur("name")}
                  error={errors.name}
                />
                <Input
                  placeholder="Enter your email"
                  value={values.email}
                  onChangeText={handleChange("email")}
                  onBlur={() => handleBlur("email")}
                  error={errors.email}
                  icon="mail"
                />
                <Input
                  placeholder="Enter your password"
                  value={values.password}
                  onChangeText={handleChange("password")}
                  onBlur={() => handleBlur("password")}
                  error={errors.password}
                  secureTextEntry
                  icon="lock-closed"
                />
                <Input
                  placeholder="Confirm your password"
                  value={values.confirmPassword}
                  onChangeText={handleChange("confirmPassword")}
                  onBlur={() => handleBlur("confirmPassword")}
                  error={errors.confirmPassword}
                  secureTextEntry
                  icon="lock-closed"
                />
                <Button
                  title="Register"
                  onPress={handleSubmit}
                  disabled={mutation.isPending}
                />
              </>
            </FormLayout>
          )}
        </Formik>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
    backgroundColor: colors.background,
    alignItems: "center",
  },
  keyboardView: {
    width: "100%",
  },
});
