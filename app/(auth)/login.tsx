import { Button, Input } from "@/components";
import FormLayout from "@/components/form-layout";
import HeaderBack from "@/components/header-back";
import api from "@/lib/axios";
import { useAuthStore } from "@/store/auth";
import { colors } from "@/utils/colors";
import { fonts } from "@/utils/fonts";
import { useMutation } from "@tanstack/react-query";
import { router } from "expo-router";
import { Formik } from "formik";
import {
  KeyboardAvoidingView,
  Platform,
  StyleSheet,
  Text,
  View,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import Toast from "react-native-toast-message";
import * as yup from "yup";

const schema = yup.object().shape({
  email: yup.string().email().required(),
  password: yup.string().min(6).required(),
});

type LoginFormValues = {
  email: string;
  password: string;
};
export default function Login() {
  const { login } = useAuthStore();

  const mutation = useMutation({
    mutationFn: async (values: LoginFormValues) =>
      await api.post("/auth/login", values),
    onSuccess: (data) => {
      if (data.data.data) {
        const user = data.data.data.user;
        if (user.verified) {
          login({
            token: data.data.data.access_token,
            id: user.id,
            email: user.email,
            role: user.role,
            name: user.username,
            verified: true,
          });
          Toast.show({
            type: "success",
            text1: "Success",
            text2: "Login successful",
          });
          router.push("/(main)/home");
        } else {
          router.push("/(auth)/verification");
        }
      }
    },
    onError: (error: any) => {
      console.log(error.response?.data.error);
      Toast.show({
        type: "error",
        text1: "Error",
        text2: error.response?.data.error,
      });
    },
  });
  const hadleSubmit = (values: LoginFormValues) => {
    mutation.mutate(values);
  };

  return (
    <SafeAreaView style={styles.container}>
      <HeaderBack />
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.keyboardView}
      >
        <Formik
          initialValues={{ email: "", password: "" }}
          validationSchema={schema}
          onSubmit={hadleSubmit}
        >
          {({ handleChange, handleBlur, handleSubmit, values, errors }) => (
            <FormLayout
              title="Login"
              subtitle="Enter your email and password to secure your account and manage services"
              footerText="Don't have an account?"
              footerLink="/(auth)/register"
              footerLinkText="Signup here"
            >
              <>
                <Input
                  placeholder="Enter your email"
                  value={values.email}
                  onChangeText={handleChange("email")}
                  onBlur={() => handleBlur("email")}
                  error={errors.email}
                  icon="mail"
                />

                <Input
                  placeholder="Enter your password"
                  value={values.password}
                  onChangeText={handleChange("password")}
                  onBlur={() => handleBlur("password")}
                  error={errors.password}
                  secureTextEntry
                  icon="lock-closed"
                />
                <View style={{ marginBottom: 16 }}>
                  {/* forgot password */}
                  <Text
                    onPress={() => router.push("/(auth)/forgot-password")}
                    style={{
                      color: colors.text,
                      fontSize: 14,
                      fontFamily: fonts.bold,
                      textAlign: "right",
                    }}
                  >
                    Forgot password?
                  </Text>
                </View>
                <Button
                  disabled={mutation.isPending}
                  title="Login"
                  onPress={handleSubmit}
                />
              </>
            </FormLayout>
          )}
        </Formik>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
    // justifyContent: "center",
    paddingHorizontal: 16,
    alignItems: "center",
  },
  keyboardView: {
    width: "100%",
  },
});
