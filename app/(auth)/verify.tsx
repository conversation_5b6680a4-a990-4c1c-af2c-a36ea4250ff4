import { Button, Input } from "@/components";
import FormLayout from "@/components/form-layout";
import HeaderBack from "@/components/header-back";
import api from "@/lib/axios";
import { useAuthStore } from "@/store/auth";
import { colors } from "@/utils/colors";
import { useMutation } from "@tanstack/react-query";
import { router } from "expo-router";
import { Formik } from "formik";
import { KeyboardAvoidingView, Platform, StyleSheet } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import Toast from "react-native-toast-message";
import * as yup from "yup";
const schema = yup.object().shape({
  code: yup.string().required(),
});
type VerifyFormValues = {
  code: string;
};
export default function Verify() {
  const { login } = useAuthStore();
  const mutation = useMutation({
    mutationFn: (values: VerifyFormValues) => api.post("/auth/verify", values),
    onSuccess: (data) => {
      if (data.data.data.user) {
        const user = data.data.data.user;
        login({
          token: data.data.data.access_token,
          email: user.email,
          name: user.username,
          id: user.id,
          verified: user.verified,
          role: user.role,
        });

        Toast.show({
          type: "success",
          text1: "Success",
          text2: "Account Vefied",
        });

        router.push("/(main)/home");
      }
    },
    onError: (error: any) => {
      Toast.show({
        type: "error",
        text1: "Error",
        text2: error.response?.data.error,
      });
    },
  });
  const hadleSubmit = (values: VerifyFormValues) => {
    mutation.mutate(values);
  };
  return (
    <SafeAreaView style={styles.container}>
      <HeaderBack />
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.keyboardView}
      >
        <Formik
          initialValues={{ code: "" }}
          validationSchema={schema}
          onSubmit={hadleSubmit}
        >
          {({ handleChange, handleBlur, handleSubmit, values, errors }) => (
            <KeyboardAvoidingView>
              <FormLayout
                title="Verify"
                subtitle="Enter code from your email to verify your account and gain full access of all features"
                footerLink={"/(auth)/login"}
                footerLinkText="Login"
                footerText="Back to login"
              >
                <>
                  <Input
                    placeholder="Enter your code"
                    value={values.code}
                    onChangeText={handleChange("code")}
                    onBlur={() => handleBlur("code")}
                    error={errors.code}
                    icon="code"
                  />
                  <Button
                    title="Continue"
                    onPress={handleSubmit}
                    disabled={mutation.isPending}
                  />
                </>
              </FormLayout>
            </KeyboardAvoidingView>
          )}
        </Formik>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
    backgroundColor: colors.background,
    alignItems: "center",
  },
  keyboardView: {
    width: "100%",
  },
});
