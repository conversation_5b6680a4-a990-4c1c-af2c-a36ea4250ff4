import { Stack } from "expo-router";

export default function AuthLayout() {
  return (
    <Stack screenOptions={{ headerShown: false }}>
      <Stack.Screen
        name="login"
        options={{ animation: "slide_from_bottom", headerShown: false }}
      />
      <Stack.Screen
        name="register"
        options={{ animation: "slide_from_bottom", headerShown: false }}
      />
      <Stack.Screen
        name="verify"
        options={{ animation: "slide_from_bottom" }}
      />
      <Stack.Screen
        name="verification"
        options={{ animation: "slide_from_bottom" }}
      />
      <Stack.Screen
        name="forgot-password"
        options={{ animation: "slide_from_bottom" }}
      />
      <Stack.Screen
        name="reset-password"
        options={{ animation: "slide_from_bottom" }}
      />
    </Stack>
  );
}
