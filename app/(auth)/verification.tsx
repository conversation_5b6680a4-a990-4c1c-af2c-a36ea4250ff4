import { Button, Input } from "@/components";
import FormLayout from "@/components/form-layout";
import HeaderBack from "@/components/header-back";
import api from "@/lib/axios";
import { useMutation } from "@tanstack/react-query";
import { router } from "expo-router";
import { Formik } from "formik";
import { KeyboardAvoidingView, Platform, StyleSheet } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import Toast from "react-native-toast-message";
import * as yup from "yup";

const schema = yup.object().shape({
  email: yup.string().required(),
});

type VerifyFormValues = {
  email: string;
};

export default function Verify() {
  const mutation = useMutation({
    mutationFn: (values: VerifyFormValues) =>
      api.post("/auth/get-code", values),
    onSuccess: (data) => {
      Toast.show({
        type: "success",
        text1: "success",
        text2: "Check your email code sent",
      });
      router.push("/(auth)/verify");
    },
    onError: (error: any) => {
      Toast.show({
        type: "error",
        text1: "Error",
        text2: error.response?.data.error,
      });
    },
  });

  const hadleSubmit = (values: VerifyFormValues) => {
    mutation.mutate(values);
  };

  return (
    <SafeAreaView style={styles.container}>
      <HeaderBack />
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.keyboardView}
      >
        <Formik
          initialValues={{ email: "" }}
          validationSchema={schema}
          onSubmit={hadleSubmit}
        >
          {({ handleChange, handleBlur, handleSubmit, values, errors }) => (
            <FormLayout
              title="Get Code"
              subtitle="Enter you email to recieve verification code to verify your account"
            >
              <>
                <Input
                  placeholder="Enter your Email"
                  value={values.email}
                  icon="mail"
                  onChangeText={handleChange("email")}
                  onBlur={() => handleBlur("email")}
                  error={errors.email}
                />
                <Button
                  title="Continue"
                  onPress={handleSubmit}
                  disabled={mutation.isPending}
                />
              </>
            </FormLayout>
          )}
        </Formik>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
    alignItems: "center",
  },
  keyboardView: {
    width: "100%",
  },
});
