import { Button, Input } from "@/components";
import HeaderBack from "@/components/header-back";
import { Formik } from "formik";
import { KeyboardAvoidingView, Platform, StyleSheet } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import * as yup from "yup";

const schema = yup.object().shape({
  email: yup.string().email().required(),
});

type ForgotPasswordFormValues = {
  email: string;
};

export default function ForgotPassword() {
  const hadleSubmit = (values: ForgotPasswordFormValues) => {
    console.log(values);
  };

  return (
    <SafeAreaView style={styles.container}>
      <HeaderBack />
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.keyboardView}
      >
        <Formik
          initialValues={{ email: "" }}
          validationSchema={schema}
          onSubmit={hadleSubmit}
        >
          {({ handleChange, handleBlur, handleSubmit, values, errors }) => (
            <>
              <Input
                label="Email"
                placeholder="Enter your email"
                value={values.email}
                onChangeText={handleChange("email")}
                onBlur={() => handleBlur("email")}
                error={errors.email}
                icon="mail-outline"
              />
              <Button title="Send" onPress={handleSubmit} />
            </>
          )}
        </Formik>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "center",
    paddingHorizontal: 16,
    alignItems: "center",
  },
  keyboardView: {
    width: "100%",
  },
});
