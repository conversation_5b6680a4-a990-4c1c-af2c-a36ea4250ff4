import { Button, Input } from "@/components";
import FormLayout from "@/components/form-layout";
import { Formik } from "formik";
import { KeyboardAvoidingView, Platform, StyleSheet } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import * as yup from "yup";

const schema = yup.object().shape({
  password: yup.string().min(6).required(),
  confirmPassword: yup.string().min(6).required(),
});

type ResetPasswordFormValues = {
  password: string;
  confirmPassword: string;
};

export default function ResetPassword() {
  const hadleSubmit = (values: ResetPasswordFormValues) => {
    console.log(values);
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.keyboardView}
      >
        <Formik
          initialValues={{ password: "", confirmPassword: "" }}
          validationSchema={schema}
          onSubmit={hadleSubmit}
        >
          {({ handleChange, handleBlur, handleSubmit, values, errors }) => (
            <FormLayout title="Reset Password">
              <>
                <Input
                  placeholder="Enter your new password"
                  value={values.password}
                  onChangeText={handleChange("password")}
                  onBlur={() => handleBlur("password")}
                  error={errors.password}
                  secureTextEntry
                  icon="lock-closed"
                />
                <Input
                  label="Confirm Password"
                  placeholder="Confirm your new password"
                  value={values.confirmPassword}
                  onChangeText={handleChange("confirmPassword")}
                  onBlur={() => handleBlur("confirmPassword")}
                  error={errors.confirmPassword}
                  secureTextEntry
                  icon="lock-closed"
                />
                <Button title="Reset Password" onPress={handleSubmit} />
              </>
            </FormLayout>
          )}
        </Formik>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "center",
    paddingHorizontal: 16,
    alignItems: "center",
  },
  keyboardView: {
    width: "100%",
  },
});
