import HeaderBack from "@/components/header-back";
import api from "@/lib/axios";
import { useAuthStore } from "@/store/auth";
import { ProfileResponse } from "@/types/profile";
import { colors } from "@/utils/colors";
import { fonts } from "@/utils/fonts";
import { Ionicons } from "@expo/vector-icons";
import { useQuery } from "@tanstack/react-query";
import { router } from "expo-router";
import {
  Image,
  Pressable,
  ScrollView,
  StyleSheet,
  Text,
  View,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

const UsersProfile = () => {
  const { logout } = useAuthStore();
  const { data } = useQuery<ProfileResponse>({
    queryKey: ["profile"],
    queryFn: async () => api.get("/profile").then((res) => res.data),
  });

  return (
    <SafeAreaView style={styles.safeArea}>
      <ScrollView contentContainerStyle={styles.container}>
        <HeaderBack title="Profile" />

        <View style={styles.profileHeader}>
          <View style={styles.avatarContainer}>
            <Image
              style={styles.avatar}
              source={{
                uri: data?.data.avatar || "https://via.placeholder.com/100",
              }}
            />
            <Pressable
              style={styles.editButton}
              onPress={() => router.push("/profile/edit")}
            >
              <Ionicons name="pencil" size={16} color={colors.white} />
            </Pressable>
          </View>
          <Text style={styles.name}>
            {data?.data.firstName} {data?.data.lastName}
          </Text>
          <Text style={styles.email}>{data?.data.email}</Text>
          {data?.data.verified && (
            <View style={styles.verifiedBadge}>
              <Ionicons
                name="checkmark-circle"
                size={16}
                color={colors.primary}
              />
              <Text style={styles.verifiedText}>Verified Account</Text>
            </View>
          )}
        </View>

        <View style={styles.infoSection}>
          <View style={styles.infoRow}>
            <Ionicons name="person-outline" size={24} color={colors.primary} />
            <Text style={styles.infoText}>@{data?.data.username}</Text>
          </View>
          <View style={styles.infoRow}>
            <Ionicons name="mail-outline" size={24} color={colors.primary} />
            <Text style={styles.infoText}>{data?.data.email}</Text>
          </View>
          <View style={styles.infoRow}>
            <Ionicons name="time-outline" size={24} color={colors.primary} />
            <Text style={styles.infoText}>
              Joined {new Date(data?.data.createdAt || "").toLocaleDateString()}
            </Text>
          </View>
        </View>

        <Pressable style={styles.logoutButton} onPress={() => logout()}>
          <Ionicons name="log-out-outline" size={24} color={colors.white} />
          <Text style={styles.logoutText}>Logout</Text>
        </Pressable>
      </ScrollView>
    </SafeAreaView>
  );
};

export default UsersProfile;

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: colors.background,
  },
  container: {
    flex: 1,
    paddingHorizontal: 16,
  },
  profileHeader: {
    alignItems: "center",
    marginTop: 20,
  },
  avatarContainer: {
    position: "relative",
    marginBottom: 16,
  },
  avatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
  },
  editButton: {
    position: "absolute",
    right: 0,
    top: 0,
    backgroundColor: colors.primary,
    padding: 8,
    borderRadius: 20,
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  name: {
    fontSize: 24,
    fontFamily: fonts.bold,
    color: colors.text,
    marginBottom: 4,
  },
  email: {
    fontSize: 16,
    fontFamily: fonts.regular,
    color: colors.white,
    marginBottom: 12,
  },
  verifiedBadge: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: colors.white,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginBottom: 24,
    gap: 6,
  },
  verifiedText: {
    fontSize: 14,
    fontFamily: fonts.regular,
    color: colors.primary,
  },
  infoSection: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
  },
  infoRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
    gap: 12,
  },
  infoText: {
    flex: 1,
    fontSize: 16,
    fontFamily: fonts.regular,
    color: colors.text,
  },
  logoutButton: {
    backgroundColor: colors.danger,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    padding: 16,
    borderRadius: 12,
    gap: 8,
  },
  logoutText: {
    color: colors.white,
    fontSize: 16,
    fontFamily: fonts.bold,
  },
});
