import { Button, Input } from "@/components";
import HeaderBack from "@/components/header-back";
import api from "@/lib/axios";
import { ProfileResponse } from "@/types/profile";
import { colors } from "@/utils/colors";
import { useMutation, useQuery } from "@tanstack/react-query";
import * as ImagePicker from "expo-image-picker";
import { router } from "expo-router";
import { Formik } from "formik";
import {
  Image,
  KeyboardAvoidingView,
  Platform,
  Pressable,
  StyleSheet,
  Text,
  View,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import Toast from "react-native-toast-message";
import * as yup from "yup";

const schema = yup.object().shape({
  firstName: yup.string().required("First name is required"),
  lastName: yup.string().required("Last name is required"),
});

type ProfileFormValues = {
  firstName: string;
  lastName: string;
};

export default function EditProfile() {
  const { data, refetch } = useQuery<ProfileResponse>({
    queryKey: ["profile"],
    queryFn: async () => api.get("/profile").then((res) => res.data),
  });

  const mutation = useMutation({
    mutationFn: (values: ProfileFormValues) =>
      api.patch("/profile", values).then((res) => res.data),
    onSuccess: () => {
      Toast.show({
        type: "success",
        text1: "Success",
        text2: "Profile updated successfully",
      });
      router.back();
    },
    onError: (error: any) => {
      Toast.show({
        type: "error",
        text1: "Error",
        text2: error.response?.data?.message || "Something went wrong",
      });
    },
  });

  const uploadMutation = useMutation({
    mutationFn: async (imageUri: string) => {
      const formData = new FormData();
      formData.append("avatar", {
        uri: imageUri,
        type: "image/jpeg",
        name: "avatar.jpg",
      } as any);

      return api.put("/profile/avatar", formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });
    },
    onSuccess: () => {
      Toast.show({
        type: "success",
        text1: "Success",
        text2: "Profile picture updated successfully",
      });
      refetch();
    },
    onError: (error: any) => {
      Toast.show({
        type: "error",
        text1: "Error",
        text2: error.response?.data?.message || "Failed to upload image",
      });
    },
  });

  const handleImagePick = async () => {
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: "images",
      allowsEditing: true,
      aspect: [1, 1],
      quality: 1,
    });

    if (!result.canceled && result.assets[0].uri) {
      uploadMutation.mutate(result.assets[0].uri);
    }
  };

  const handleSubmit = (values: ProfileFormValues) => {
    mutation.mutate(values);
  };

  return (
    <SafeAreaView style={styles.container}>
      <HeaderBack title="Edit Profile" />
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.keyboardView}
      >
        <Pressable style={styles.avatarContainer} onPress={handleImagePick}>
          <Image
            style={styles.avatar}
            source={{
              uri: data?.data.avatar || "https://via.placeholder.com/100",
            }}
          />
          <View style={styles.avatarOverlay}>
            <Text style={styles.avatarText}>Change Photo</Text>
          </View>
        </Pressable>

        <Formik
          initialValues={{
            firstName: data?.data.firstName || "",
            lastName: data?.data.lastName || "",
          }}
          validationSchema={schema}
          onSubmit={handleSubmit}
          enableReinitialize
        >
          {({ handleChange, handleBlur, handleSubmit, values, errors }) => (
            <>
              <Input
                placeholder="Enter your first name"
                value={values.firstName}
                onChangeText={handleChange("firstName")}
                onBlur={() => handleBlur("firstName")}
                error={errors.firstName}
                icon="person"
              />
              <Input
                placeholder="Enter your last name"
                value={values.lastName}
                onChangeText={handleChange("lastName")}
                onBlur={() => handleBlur("lastName")}
                error={errors.lastName}
                icon="person"
              />
              <Input editable={false} value={data?.data.email} icon="mail" />
              <Button
                title="Save Changes"
                onPress={handleSubmit}
                disabled={mutation.isPending || uploadMutation.isPending}
              />
            </>
          )}
        </Formik>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  keyboardView: {
    flex: 1,
    padding: 16,
  },
  note: {
    color: colors.text,
    fontSize: 12,
    marginTop: -8,
    marginBottom: 16,
    opacity: 0.7,
  },
  avatarContainer: {
    alignSelf: "center",
    marginBottom: 24,
    position: "relative",
  },
  avatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
  },
  avatarOverlay: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: "rgba(0,0,0,0.6)",
    padding: 4,
    borderBottomLeftRadius: 50,
    borderBottomRightRadius: 50,
  },
  avatarText: {
    color: colors.white,
    fontSize: 12,
    textAlign: "center",
  },
});
