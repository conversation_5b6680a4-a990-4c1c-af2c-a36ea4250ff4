import NotificationContextProvider from "@/components/notification-context";
import Tanstack from "@/components/tanstack";
import ToastProvider from "@/components/toast-provider";
import WebSocketProvider from "@/components/websoket-provider";
import { useAuthStore } from "@/store/auth";
import { useFonts } from "expo-font";
import * as Notifications from "expo-notifications";
import { router, Stack, useSegments } from "expo-router";
import { useEffect, useState } from "react";
import { StatusBar } from "react-native";
import { GestureHandlerRootView } from "react-native-gesture-handler";

Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldPlaySound: true,
    shouldSetBadge: true,
    shouldShowBanner: true,
    shouldShowList: true,
  }),
});

export default function RootLayout() {
  const [fontsLoaded] = useFonts({
    pt_bold: require("@/assets/fonts/PTSans-Bold.ttf"),
    pt_regular: require("@/assets/fonts/PTSans-Regular.ttf"),
  });

  const { isAuthenticated } = useAuthStore();
  const segments = useSegments();
  const [isReady, setIsReady] = useState(false);

  // Wait for fonts to load before marking as ready
  useEffect(() => {
    if (fontsLoaded) {
      setIsReady(true);
    }
  }, [fontsLoaded]);

  // Handle navigation only after component is ready
  useEffect(() => {
    if (!isReady) return;

    if (segments[0] !== "(auth)" && !isAuthenticated) {
      router.replace("/(auth)/login");
    }
  }, [segments, isAuthenticated, isReady]);

  // Don't render anything until fonts are loaded and component is ready
  if (!isReady) {
    return null; // Or return a loading component like <SplashScreen />
  }

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <StatusBar
        barStyle="dark-content"
        backgroundColor="transparent"
        translucent
      />
      <NotificationContextProvider>
        <Tanstack>
          <ToastProvider>
            <WebSocketProvider>
              <Stack>
                <Stack.Protected guard={!isAuthenticated}>
                  <Stack.Screen
                    name="(auth)"
                    options={{ headerShown: false }}
                  />
                </Stack.Protected>
                <Stack.Protected guard={isAuthenticated}>
                  <Stack.Screen name="index" options={{ headerShown: false }} />
                  <Stack.Screen
                    name="(main)"
                    options={{ headerShown: false }}
                  />
                  <Stack.Screen
                    name="notifications"
                    options={{
                      headerShown: false,
                      presentation: "modal",
                      animation: "slide_from_bottom",
                    }}
                  />
                  <Stack.Screen
                    name="profile/index"
                    options={{
                      headerShown: false,
                      presentation: "modal",
                      animation: "slide_from_bottom",
                    }}
                  />
                  <Stack.Screen
                    name="add-org"
                    options={{
                      headerShown: false,
                      presentation: "modal",
                      animation: "slide_from_left",
                    }}
                  />
                  <Stack.Screen
                    name="profile/edit"
                    options={{
                      headerShown: false,
                      presentation: "modal",
                      animation: "slide_from_right",
                    }}
                  />
                  <Stack.Screen
                    name="session/new-session"
                    options={{
                      headerShown: false,
                      presentation: "modal",
                      animation: "slide_from_right",
                    }}
                  />
                  <Stack.Screen
                    name="org/[id]/single"
                    options={{
                      headerShown: false,
                      presentation: "modal",
                      animation: "slide_from_right",
                    }}
                  />
                  <Stack.Screen
                    name="org/[id]/invite"
                    options={{
                      headerShown: false,
                      presentation: "modal",
                      animation: "slide_from_right",
                    }}
                  />
                  <Stack.Screen
                    name="join-org"
                    options={{
                      headerShown: false,
                      presentation: "modal",
                      animation: "slide_from_right",
                    }}
                  />
                  <Stack.Screen
                    name="session/[id]/check-in"
                    options={{
                      headerShown: false,
                      presentation: "modal",
                      animation: "slide_from_right",
                    }}
                  />
                  <Stack.Screen
                    name="session/[id]/check-out"
                    options={{
                      headerShown: false,
                      presentation: "modal",
                      animation: "slide_from_right",
                    }}
                  />
                  <Stack.Screen
                    name="session/[id]/single"
                    options={{
                      headerShown: false,
                      presentation: "modal",
                      animation: "slide_from_bottom",
                    }}
                  />
                </Stack.Protected>
              </Stack>
            </WebSocketProvider>
          </ToastProvider>
        </Tanstack>
      </NotificationContextProvider>
    </GestureHandlerRootView>
  );
}
