import { colors } from "@/utils/colors";
import { fonts } from "@/utils/fonts";
import { Ionicons } from "@expo/vector-icons";
import { router, Tabs } from "expo-router";
import { Pressable, View } from "react-native";
export default function RootLayout() {
  return (
    <Tabs
      screenOptions={{
        headerStyle: {
          backgroundColor: colors.white,
          shadowColor: "transparent",
        },
        headerShadowVisible: false,
        headerTitleStyle: {
          fontFamily: fonts.bold,
          fontSize: 18,
          color: colors.secondary,
        },
        tabBarStyle: {
          backgroundColor: colors.white,
          borderTopColor: "transparent",
          borderBottomColor: "transparent",
          borderBottomWidth: 0,
          paddingTop: 8,
          height: 80,
          borderStartStartRadius: 20,
          borderEndStartRadius: 20,
          elevation: 0,
          borderTopWidth: 0,
        },
        tabBarActiveTintColor: colors.primary,
        tabBarInactiveTintColor: colors.text,
      }}
    >
      <Tabs.Screen
        name="home"
        options={{
          title: "Home",
          headerShown: false,
          tabBarIcon: ({ focused }) => (
            <Ionicons
              name={focused ? "home" : "home-outline"}
              size={24}
              color={focused ? colors.primary : colors.secondary}
            />
          ),
        }}
      />

      <Tabs.Screen
        name="sessions"
        options={{
          title: "Sessions",
          headerRight(props) {
            return (
              <View style={{ flexDirection: "row", gap: 16, marginRight: 16 }}>
                <Pressable
                  style={{
                    backgroundColor: colors.primary,
                    borderRadius: 15,
                    padding: 6,
                    elevation: 20,
                    shadowColor: "#ccc",
                    shadowOffset: {
                      width: 0,
                      height: 20,
                    },
                    shadowOpacity: 0.29,
                  }}
                  onPress={() => router.push("/session/new-session")}
                >
                  <Ionicons name="add-outline" size={24} color={colors.white} />
                </Pressable>
              </View>
            );
          },
          tabBarIcon: ({ focused }) => (
            <Ionicons
              name={focused ? "calendar" : "calendar-outline"}
              size={24}
              color={focused ? colors.primary : colors.secondary}
            />
          ),
        }}
      />
      <Tabs.Screen
        name="organisations"
        options={{
          title: "Organisations",
          headerRight(props) {
            return (
              <View style={{ flexDirection: "row", gap: 16, marginRight: 16 }}>
                <Pressable
                  style={{
                    backgroundColor: colors.primary,
                    borderRadius: 15,
                    padding: 6,
                    elevation: 20,
                    shadowColor: "#ccc",
                    shadowOffset: {
                      width: 0,
                      height: 20,
                    },
                    shadowOpacity: 0.29,
                  }}
                  onPress={() => router.push("/add-org")}
                >
                  <Ionicons name="add-outline" size={24} color={colors.white} />
                </Pressable>

                <Pressable
                  style={{
                    backgroundColor: colors.primary,
                    borderRadius: 15,
                    padding: 6,
                    elevation: 20,
                    shadowColor: "#ccc",
                    shadowOffset: {
                      width: 0,
                      height: 20,
                    },
                    shadowOpacity: 0.29,
                  }}
                  onPress={() => router.push("/join-org")}
                >
                  <Ionicons
                    name="enter-outline"
                    size={24}
                    color={colors.white}
                  />
                </Pressable>
              </View>
            );
          },
          tabBarIcon: ({ focused }) => (
            <Ionicons
              name={focused ? "briefcase" : "briefcase-outline"}
              size={24}
              color={focused ? colors.primary : colors.secondary}
            />
          ),
        }}
      />

      <Tabs.Screen
        name="settings"
        options={{
          headerShown: false,
          title: "Settings",
          tabBarIcon: ({ focused }) => (
            <Ionicons
              name={focused ? "settings" : "settings-outline"}
              size={24}
              color={focused ? colors.primary : colors.secondary}
            />
          ),
        }}
      />
    </Tabs>
  );
}
