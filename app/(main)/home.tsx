import MainHeader from "@/components/main-header";
import OngoingSessions from "@/components/ongoing-sessions";
import { colors } from "@/utils/colors";
import { fonts } from "@/utils/fonts";
import { StyleSheet, Text, View } from "react-native";

const MainScreen = () => {
  return (
    // <SafeAreaView style={styles.container}>
    <View>
      <MainHeader />
      <View style={styles.content}>
        <Text style={styles.title}>Ongoing Sessions</Text>
        <OngoingSessions />
      </View>
    </View>
    // </SafeAreaView>
  );
};

export default MainScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  content: {
    padding: 16,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 20,
  },
  title: {
    fontSize: 24,
    fontFamily: fonts.bold,
    color: colors.secondary,
  },
  emptyState: {
    alignItems: "center",
    padding: 40,
  },
  emptyText: {
    fontFamily: fonts.regular,
    color: colors.text,
    fontSize: 16,
  },
  sessionList: {
    gap: 12,
  },
  sessionCard: {
    backgroundColor: colors.white,
    padding: 16,
    borderRadius: 12,
    elevation: 2,
    shadowColor: colors.secondary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  sessionName: {
    fontSize: 18,
    fontFamily: fonts.bold,
    color: colors.secondary,
    marginBottom: 8,
  },
  sessionDetails: {
    fontSize: 14,
    fontFamily: fonts.regular,
    color: colors.text,
    marginBottom: 4,
  },
  sessionTime: {
    fontSize: 12,
    fontFamily: fonts.regular,
    color: colors.text,
  },
});
