import HeaderBack from "@/components/header-back";
import api from "@/lib/axios";
import { colors } from "@/utils/colors";
import { fonts } from "@/utils/fonts";
import { registerForPushNotificationsAsync } from "@/utils/notification";
import { useMutation } from "@tanstack/react-query";
import { router } from "expo-router";
import { useState } from "react";
import { StyleSheet, Switch, Text, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import Toast from "react-native-toast-message";

export default function Profile() {
  const [notificationsEnabled, setNotificationsEnabled] = useState(false);
  const [expoToken, setExpoToken] = useState<string | null>(null);
  const mutation = useMutation({
    mutationFn: (values: any) =>
      api.post("/users/update-notification-token", values),
    onSuccess: (data) => {
      Toast.show({
        type: "success",
        text1: "Success",
        text2: "Profile updated successfully",
      });
      router.back();
    },
    onError: (error: any) => {
      Toast.show({
        type: "error",
        text1: "Error",
        text2: error.response?.data?.message || "Something went wrong",
      });
    },
  });

  const toggleNotifications = () => {
    registerForPushNotificationsAsync()
      .then((token) => {
        // setExpoToken(token);
        mutation.mutate({ token: token });
      })
      .catch((error) => {
        console.log(error);
      });
    setNotificationsEnabled(!notificationsEnabled);
  };
  return (
    <SafeAreaView style={styles.container}>
      <HeaderBack title="Settings" />
      <View style={styles.content}>
        <View style={styles.settingItem}>
          <Text style={styles.settingText}>Enable Notifications</Text>
          <Switch
            value={notificationsEnabled}
            onValueChange={toggleNotifications}
          />
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: colors.background,
  },
  content: {
    flex: 1,
    backgroundColor: colors.white,
    borderRadius: 20,
    padding: 8,
  },
  settingItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",

    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.textLight,
  },
  settingText: {
    fontSize: 16,
    fontFamily: fonts.bold,
    color: colors.secondary,
  },
});
