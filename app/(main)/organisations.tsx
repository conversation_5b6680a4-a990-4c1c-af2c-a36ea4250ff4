import OrganisationList from "@/components/organisation-list";
import { colors } from "@/utils/colors";
import { StyleSheet, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

export default function Organisations() {
  return (
    <SafeAreaView style={styles.container}>
      <View>
        <OrganisationList />
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
    backgroundColor: colors.background,
  },
  title: {
    fontSize: 24,
    fontFamily: "pt_bold",
    color: "#22272A",
    marginBottom: 16,
  },
});
