import { Button, Input } from "@/components";
import HeaderBack from "@/components/header-back";
import api from "@/lib/axios";
import { colors } from "@/utils/colors";
import { fonts } from "@/utils/fonts";
import { Ionicons } from "@expo/vector-icons";
import { useMutation } from "@tanstack/react-query";
import * as ImagePicker from "expo-image-picker";
import { router } from "expo-router";
import { Formik } from "formik";
import { Image, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import Toast from "react-native-toast-message";
import * as yup from "yup";
const validationSchema = yup.object().shape({
  name: yup.string().required("Organisation name is required"),
  description: yup.string().required("Description is required"),
  image: yup.string().required("Organisation image is required"),
});

type AddOrgFormValues = {
  name: string;
  description: string;
  image: string;
};
export default function AddOrg() {
  const mutation = useMutation({
    mutationFn: (values: AddOrgFormValues) => {
      const formData = new FormData();
      formData.append("name", values.name);
      formData.append("description", values.description);
      formData.append("logo", {
        uri: values.image,
        type: "image/jpeg",
        name: "image.jpg",
      } as any);
      return api.post("/organisations/create", formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });
    },
    onSuccess: (data) => {
      Toast.show({
        type: "success",
        text1: "Success",
        text2: "Organisation created successfully",
      });
      router.push("/organisations");
    },
    onError: (error: any) => {
      Toast.show({
        type: "error",
        text1: "Error",
        text2: error.response?.data.error,
      });
    },
  });
  const pickImage = async () => {
    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: "images",
      aspect: [1, 1],
      quality: 1,
      allowsEditing: true,
    });

    if (!result.canceled) {
      return result.assets[0].uri;
    }
  };

  const handleSubmit = (values: AddOrgFormValues) => {
    mutation.mutate(values);
  };

  return (
    <SafeAreaView style={styles.container}>
      <HeaderBack title="Add Organisation" />
      <Formik
        initialValues={{ name: "", description: "", image: "" }}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
      >
        {({
          handleChange,
          handleBlur,
          handleSubmit,
          values,
          setFieldValue,
          errors,
        }) => (
          <View style={styles.formContainer}>
            <TouchableOpacity
              style={styles.imageContainer}
              onPress={async () => {
                const image = await pickImage();
                if (image) {
                  setFieldValue("image", image);
                }
              }}
            >
              {values.image ? (
                <Image source={{ uri: values.image }} style={styles.image} />
              ) : (
                <View style={styles.placeholderContainer}>
                  <Ionicons name="camera" size={40} color={colors.primary} />
                </View>
              )}
            </TouchableOpacity>
            {errors.image && (
              <Text style={styles.errorText}>{errors.image}</Text>
            )}

            <Input
              placeholder="Organisation Name"
              value={values.name}
              onChangeText={handleChange("name")}
              onBlur={() => handleBlur("name")}
              error={errors.name}
            />

            <Input
              placeholder="Description"
              value={values.description}
              onChangeText={handleChange("description")}
              onBlur={() => handleBlur("description")}
              error={errors.description}
              multiline
            />

            <Button
              title="Create Organisation"
              onPress={handleSubmit}
              disabled={mutation.isPending}
            />
          </View>
        )}
      </Formik>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: colors.background,
  },
  formContainer: {
    paddingVertical: 16,
    flex: 1,
    alignItems: "center",
  },
  imageContainer: {
    width: 100,
    height: 100,
    aspectRatio: 1,
    borderRadius: 20,
    overflow: "hidden",
    marginBottom: 16,
    backgroundColor: colors.text,
  },
  image: {
    width: "100%",
    height: "100%",
  },
  placeholderContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  placeholderText: {
    marginTop: 8,
    fontFamily: fonts.regular,
    color: colors.primary,
  },
  errorText: {
    color: colors.danger,
    fontSize: 12,
    marginTop: -12,
    marginBottom: 16,
    fontFamily: fonts.regular,
  },
});
