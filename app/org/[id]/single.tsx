import HeaderBack from "@/components/header-back";
import api from "@/lib/axios";
import { SingleOrgResponse } from "@/types/organisation";
import { colors } from "@/utils/colors";
import { fonts } from "@/utils/fonts";
import { Ionicons } from "@expo/vector-icons";
import { useQuery } from "@tanstack/react-query";
import { router, useGlobalSearchParams } from "expo-router";
import {
  FlatList,
  Image,
  Pressable,
  StyleSheet,
  Text,
  View,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

const SingleOrgnisation = () => {
  const { id } = useGlobalSearchParams();

  const { data, isLoading, error } = useQuery<SingleOrgResponse>({
    queryKey: ["organisation", id],
    queryFn: () => api.get(`/organisations/${id}`).then((res) => res.data),
  });

  if (isLoading) {
    return <Text>Loading...</Text>;
  }

  console.log(data);

  return (
    <SafeAreaView style={styles.container}>
      <HeaderBack title={data?.data.name} />
      <View style={styles.content}>
        <View style={styles.imageContainer}>
          {data?.data.avatar ? (
            <Image source={{ uri: data.data.avatar }} style={styles.image} />
          ) : (
            <View style={styles.placeholderImage}>
              <Text style={styles.placeholderText}>{data?.data.name[0]}</Text>
            </View>
          )}
        </View>

        <View style={styles.descriptionContainer}>
          <Text style={styles.description}>{data?.data.description}</Text>
          <Text style={styles.code}>Code: {data?.data.code}</Text>
        </View>

        <View style={styles.actionContainer}>
          <Pressable style={styles.actionButton}>
            <Ionicons name="create-outline" size={18} color={colors.primary} />
            <Text style={styles.actionText}>Edit</Text>
          </Pressable>
          <Pressable style={styles.actionButton}>
            <Ionicons
              name="person-add-outline"
              size={18}
              onPress={() => router.push(`/org/${data?.data.id}/invite`)}
              color={colors.primary}
            />
            <Text style={styles.actionText}>Invite</Text>
          </Pressable>
          <Pressable style={[styles.actionButton, styles.deleteButton]}>
            <Ionicons name="trash-outline" size={18} color={colors.white} />
            <Text style={[styles.actionText, styles.deleteText]}>Delete</Text>
          </Pressable>
        </View>

        <View style={styles.membersContainer}>
          <Text style={styles.membersTitle}>
            Members ({data?.data.members.length})
          </Text>
          <FlatList
            data={data?.data.members}
            keyExtractor={(item) => item.id.toString()}
            renderItem={({ item }) => (
              <View style={styles.memberItem}>
                <Image
                  source={{
                    uri: item.avatar || "https://via.placeholder.com/40",
                  }}
                  style={styles.memberAvatar}
                />
                <View style={styles.memberInfo}>
                  <Text style={styles.memberName}>{item.username}</Text>
                  <Text style={styles.memberEmail}>{item.email}</Text>
                </View>
                <Text style={styles.joinDate}>
                  Joined {new Date(item.joinedAt).toLocaleDateString()}
                </Text>
              </View>
            )}
            ItemSeparatorComponent={() => <View style={styles.separator} />}
          />
        </View>
      </View>
    </SafeAreaView>
  );
};
export default SingleOrgnisation;
const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: colors.background,
  },
  content: {
    flex: 1,
  },
  imageContainer: {
    alignItems: "center",
    justifyContent: "center",
    padding: 16,
  },
  image: {
    width: 120,
    height: 120,
    borderRadius: 30,
  },
  placeholderImage: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: colors.textLight,
    alignItems: "center",
    justifyContent: "center",
  },
  placeholderText: {
    fontSize: 40,
    color: colors.text,
  },
  descriptionContainer: {
    padding: 16,
  },
  description: {
    fontSize: 16,
    fontFamily: fonts.regular,
    color: colors.text,
    marginBottom: 8,
  },
  code: {
    fontSize: 14,
    fontFamily: fonts.regular,
    color: colors.text,
  },
  actionContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    padding: 16,
    gap: 16,
  },
  actionButton: {
    flexDirection: "row",
    alignItems: "center",
    padding: 8,
    backgroundColor: colors.white,
    borderRadius: 12,
    elevation: 2,
    shadowColor: "#ccc",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  actionText: {
    marginLeft: 2,
    fontSize: 16,
    fontFamily: fonts.regular,
    color: colors.text,
  },
  deleteButton: {
    backgroundColor: colors.danger,
  },
  deleteText: {
    color: colors.white,
  },
  membersContainer: {
    padding: 16,
  },
  membersTitle: {
    fontSize: 16,
    fontFamily: fonts.bold,
    color: colors.text,
    marginBottom: 16,
  },
  memberItem: {
    flexDirection: "row",
    alignItems: "center",
    padding: 12,
    backgroundColor: colors.white,
    borderRadius: 8,
    elevation: 2,
    shadowColor: "#ccc",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  memberAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  memberInfo: {
    flex: 1,
    marginLeft: 12,
  },
  memberName: {
    fontSize: 16,
    fontFamily: fonts.bold,
    color: colors.text,
  },
  memberEmail: {
    fontSize: 14,
    fontFamily: fonts.regular,
    color: colors.text,
  },
  joinDate: {
    fontSize: 12,
    fontFamily: fonts.regular,
    color: colors.text,
  },
  separator: {
    height: 16,
  },
});
