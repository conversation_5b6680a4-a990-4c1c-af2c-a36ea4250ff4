import Badge from "@/components/badge";
import HeaderBack from "@/components/header-back";
import LoadingSpinner from "@/components/loading-spinner";
import { useSocket } from "@/hooks/useSocket";
import api from "@/lib/axios";
import { useAuthStore } from "@/store/auth";
import { SingleSessionResponse } from "@/types/organisation";
import { colors } from "@/utils/colors";
import { fonts } from "@/utils/fonts";
import { formatTime } from "@/utils/format-date";
import { useQuery } from "@tanstack/react-query";
import { useLocalSearchParams } from "expo-router";
import { useEffect, useState } from "react";
import {
  Image,
  ScrollView,
  StyleSheet,
  Text,
  View,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

export default function AttendancePage() {
  const { id } = useLocalSearchParams();
  const { user } = useAuthStore();
  const { on } = useSocket();
  const [attendance, setAttendance] = useState<any>([]);

  const { data, isLoading } = useQuery<SingleSessionResponse>({
    queryKey: ["session", id],
    queryFn: () => api.get(`/sessions/${id}`).then((res) => res.data),
  });

  useEffect(() => {
    if (data?.data.attendnace) {
      setAttendance(data.data.attendnace);
    }
  }, [data?.data.attendnace]);

  on("user_checked_in", (newAttendance) => {
    setAttendance((prev: any[]) => {
      const alreadyExists = prev.some((a) => a.userId === newAttendance.userId);
      if (alreadyExists) return prev;
      return [...prev, newAttendance];
    });
  });

  const isCreator = user?.id === data?.data.session.creatorId;

  if (isLoading) {
    return <LoadingSpinner />;
  }

  // Only show this page to creators
  if (!isCreator) {
    return (
      <SafeAreaView style={styles.container}>
        <HeaderBack title="Attendance" />
        <View style={styles.content}>
          <Text style={styles.errorText}>Access denied. Only session creators can view attendance.</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <HeaderBack title="Attendance List" />
      <ScrollView>
        <View style={styles.content}>
          <View style={styles.sessionInfo}>
            <Text style={styles.sessionTitle}>
              {data?.data.session.organisationName}
            </Text>
            <Text style={styles.sessionTime}>
              {formatTime(data?.data.session.startTime as string)} - {formatTime(data?.data.session.stopTime as string)}
            </Text>
          </View>

          <View style={styles.attendeeSection}>
            <Text style={styles.label}>Attendees ({attendance?.length || 0}):</Text>
            {attendance?.length ? (
              attendance.map((attendee: any, index: number) => (
                <View key={index} style={styles.attendeeCard}>
                  <View style={styles.attendeeProfile}>
                    {attendee.avatar ? (
                      <Image
                        source={{ uri: attendee.avatar }}
                        style={styles.attendeeAvatar}
                      />
                    ) : (
                      <View style={styles.attendeePlaceholder}>
                        <Text style={styles.placeholderText}>
                          {attendee.firstName[0]}
                        </Text>
                      </View>
                    )}
                    <View style={styles.attendeeInfo}>
                      <Text style={styles.attendeeName}>
                        {`${attendee.firstName} ${attendee.lastName}`}
                      </Text>
                      <Text style={styles.username}>
                        @{attendee.username}
                      </Text>
                    </View>
                  </View>

                  <View style={styles.attendeeTimes}>
                    <Badge
                      text={`Checked In: ${formatTime(
                        attendee.checkedInAt
                      )}`}
                      variant="info"
                    />
                    {attendee.checkedOutAt && (
                      <Badge
                        text={`Checked Out: ${formatTime(
                          attendee.checkedOutAt
                        )}`}
                        variant="error"
                      />
                    )}
                  </View>
                </View>
              ))
            ) : (
              <View style={styles.emptyState}>
                <Text style={styles.emptyText}>No attendees yet</Text>
                <Text style={styles.emptySubtext}>
                  Attendees will appear here once they check in to the session
                </Text>
              </View>
            )}
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: colors.background,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  sessionInfo: {
    alignItems: "center",
    marginBottom: 24,
    backgroundColor: colors.white,
    padding: 16,
    borderRadius: 12,
  },
  sessionTitle: {
    fontSize: 20,
    fontFamily: fonts.bold,
    color: colors.text,
    marginBottom: 8,
  },
  sessionTime: {
    fontSize: 16,
    fontFamily: fonts.regular,
    color: colors.textLight,
  },
  attendeeSection: {
    flex: 1,
  },
  label: {
    fontSize: 18,
    fontFamily: fonts.bold,
    color: colors.text,
    marginBottom: 16,
  },
  attendeeCard: {
    backgroundColor: colors.white,
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
    shadowColor: "#ccc",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  attendeeProfile: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
  },
  attendeeAvatar: {
    width: 50,
    height: 50,
    borderRadius: 12,
  },
  attendeePlaceholder: {
    width: 50,
    height: 50,
    borderRadius: 12,
    backgroundColor: colors.textLight,
    alignItems: "center",
    justifyContent: "center",
  },
  placeholderText: {
    fontSize: 20,
    color: colors.text,
    fontFamily: fonts.bold,
  },
  attendeeInfo: {
    marginLeft: 12,
    flex: 1,
  },
  attendeeName: {
    fontSize: 16,
    fontFamily: fonts.bold,
    color: colors.text,
  },
  username: {
    fontSize: 14,
    fontFamily: fonts.regular,
    color: colors.textLight,
  },
  attendeeTimes: {
    gap: 8,
  },
  emptyState: {
    alignItems: "center",
    justifyContent: "center",
    padding: 32,
    backgroundColor: colors.white,
    borderRadius: 12,
  },
  emptyText: {
    fontSize: 18,
    fontFamily: fonts.bold,
    color: colors.textLight,
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    fontFamily: fonts.regular,
    color: colors.textLight,
    textAlign: "center",
  },
  errorText: {
    fontSize: 16,
    fontFamily: fonts.regular,
    color: colors.textLight,
    textAlign: "center",
    marginTop: 32,
  },
});
