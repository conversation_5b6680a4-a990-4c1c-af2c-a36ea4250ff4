import Badge from "@/components/badge";
import HeaderBack from "@/components/header-back";
import LoadingSpinner from "@/components/loading-spinner";
import { useSocket } from "@/hooks/useSocket";
import api from "@/lib/axios";
import { useAuthStore } from "@/store/auth";
import { SingleSessionResponse } from "@/types/organisation";
import { colors } from "@/utils/colors";
import { fonts } from "@/utils/fonts";
import { formatTime } from "@/utils/format-date";
import { Ionicons } from "@expo/vector-icons";
import { useQuery } from "@tanstack/react-query";
import { useLocalSearchParams } from "expo-router";
import { useEffect, useState } from "react";
import {
  Image,
  RefreshControl,
  ScrollView,
  StyleSheet,
  Text,
  View,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

export default function AttendancePage() {
  const { id } = useLocalSearchParams();
  const { user } = useAuthStore();
  const { on } = useSocket();
  const [attendance, setAttendance] = useState<any>([]);
  const [refreshing, setRefreshing] = useState(false);

  const { data, isLoading, refetch } = useQuery<SingleSessionResponse>({
    queryKey: ["session", id],
    queryFn: () => api.get(`/sessions/${id}`).then((res) => res.data),
  });

  useEffect(() => {
    if (data?.data.attendnace) {
      setAttendance(data.data.attendnace);
    }
  }, [data?.data.attendnace]);

  on("user_checked_in", (newAttendance) => {
    setAttendance((prev: any[]) => {
      const alreadyExists = prev.some((a) => a.userId === newAttendance.userId);
      if (alreadyExists) return prev;
      return [...prev, newAttendance];
    });
  });

  const onRefresh = async () => {
    setRefreshing(true);
    await refetch();
    setRefreshing(false);
  };

  const isCreator = user?.id === data?.data.session.creatorId;
  const isSessionExpired =
    new Date() > new Date(data?.data.session.stopTime || "");

  // Calculate statistics
  const totalAttendees = attendance?.length || 0;
  const checkedOutCount =
    attendance?.filter((a: any) => a.checkedOutAt)?.length || 0;
  const stillPresentCount = totalAttendees - checkedOutCount;

  if (isLoading) {
    return <LoadingSpinner />;
  }

  // Only show this page to creators
  if (!isCreator) {
    return (
      <SafeAreaView style={styles.container}>
        <HeaderBack title="Attendance" />
        <View style={styles.content}>
          <Text style={styles.errorText}>
            Access denied. Only session creators can view attendance.
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <HeaderBack title="Attendance Overview" />
      <ScrollView
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.content}>
          {/* Session Header */}
          <View style={styles.sessionHeader}>
            <View style={styles.sessionInfo}>
              <Ionicons
                name="business-outline"
                size={24}
                color={colors.primary}
              />
              <View style={styles.sessionDetails}>
                <Text style={styles.sessionTitle}>
                  {data?.data.session.organisationName}
                </Text>
                <Text style={styles.sessionTime}>
                  {formatTime(data?.data.session.startTime as string)} -{" "}
                  {formatTime(data?.data.session.stopTime as string)}
                </Text>
              </View>
            </View>
            <View style={styles.sessionStatus}>
              <Badge
                text={isSessionExpired ? "Ended" : "Active"}
                variant={isSessionExpired ? "error" : "success"}
              />
            </View>
          </View>

          {/* Statistics Cards */}
          <View style={styles.statsContainer}>
            <View style={styles.statCard}>
              <Ionicons
                name="people-outline"
                size={28}
                color={colors.primary}
              />
              <Text style={styles.statNumber}>{totalAttendees}</Text>
              <Text style={styles.statLabel}>Total Attendees</Text>
            </View>
            <View style={styles.statCard}>
              <Ionicons
                name="checkmark-circle-outline"
                size={28}
                color="#10B981"
              />
              <Text style={styles.statNumber}>{stillPresentCount}</Text>
              <Text style={styles.statLabel}>Still Present</Text>
            </View>
            <View style={styles.statCard}>
              <Ionicons name="exit-outline" size={28} color="#F59E0B" />
              <Text style={styles.statNumber}>{checkedOutCount}</Text>
              <Text style={styles.statLabel}>Checked Out</Text>
            </View>
          </View>

          {/* Attendees List */}
          <View style={styles.attendeeSection}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Attendees</Text>
              <View style={styles.attendeeCount}>
                <Text style={styles.countText}>{totalAttendees}</Text>
              </View>
            </View>
            {attendance?.length ? (
              attendance.map((attendee: any, index: number) => (
                <View key={index} style={styles.attendeeCard}>
                  <View style={styles.attendeeProfile}>
                    <View style={styles.avatarContainer}>
                      {attendee.avatar ? (
                        <Image
                          source={{ uri: attendee.avatar }}
                          style={styles.attendeeAvatar}
                        />
                      ) : (
                        <View style={styles.attendeePlaceholder}>
                          <Text style={styles.placeholderText}>
                            {attendee.firstName[0]}
                          </Text>
                        </View>
                      )}
                      <View
                        style={[
                          styles.statusIndicator,
                          {
                            backgroundColor: attendee.checkedOutAt
                              ? "#F59E0B"
                              : "#10B981",
                          },
                        ]}
                      >
                        <Ionicons
                          name={
                            attendee.checkedOutAt
                              ? "exit-outline"
                              : "checkmark-circle"
                          }
                          size={12}
                          color="white"
                        />
                      </View>
                    </View>
                    <View style={styles.attendeeInfo}>
                      <View style={styles.nameRow}>
                        <Text style={styles.attendeeName}>
                          {`${attendee.firstName} ${attendee.lastName}`}
                        </Text>
                        <View
                          style={[
                            styles.statusBadge,
                            {
                              backgroundColor: attendee.checkedOutAt
                                ? "#FEF3C7"
                                : "#D1FAE5",
                            },
                          ]}
                        >
                          <Text
                            style={[
                              styles.statusText,
                              {
                                color: attendee.checkedOutAt
                                  ? "#F59E0B"
                                  : "#10B981",
                              },
                            ]}
                          >
                            {attendee.checkedOutAt ? "Left" : "Present"}
                          </Text>
                        </View>
                      </View>
                      <Text style={styles.username}>@{attendee.username}</Text>
                    </View>
                  </View>

                  <View style={styles.timeDetails}>
                    <View style={styles.timeRow}>
                      <Ionicons
                        name="log-in-outline"
                        size={16}
                        color="#10B981"
                      />
                      <Text style={styles.timeLabel}>Check In:</Text>
                      <Text style={styles.timeValue}>
                        {formatTime(attendee.checkedInAt)}
                      </Text>
                    </View>
                    {attendee.checkedOutAt && (
                      <View style={styles.timeRow}>
                        <Ionicons
                          name="log-out-outline"
                          size={16}
                          color="#F59E0B"
                        />
                        <Text style={styles.timeLabel}>Check Out:</Text>
                        <Text style={styles.timeValue}>
                          {formatTime(attendee.checkedOutAt)}
                        </Text>
                      </View>
                    )}
                  </View>
                </View>
              ))
            ) : (
              <View style={styles.emptyState}>
                <Ionicons
                  name="people-outline"
                  size={64}
                  color={colors.textLight}
                />
                <Text style={styles.emptyText}>No attendees yet</Text>
                <Text style={styles.emptySubtext}>
                  Attendees will appear here once they check in to the session
                </Text>
              </View>
            )}
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  // Session Header Styles
  sessionHeader: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  sessionInfo: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  sessionDetails: {
    marginLeft: 12,
    flex: 1,
  },
  sessionTitle: {
    fontSize: 20,
    fontFamily: fonts.bold,
    color: colors.text,
    marginBottom: 4,
  },
  sessionTime: {
    fontSize: 14,
    fontFamily: fonts.regular,
    color: colors.textLight,
  },
  sessionStatus: {
    marginLeft: 12,
  },
  // Statistics Styles
  statsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 24,
    gap: 12,
  },
  statCard: {
    flex: 1,
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: 16,
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  statNumber: {
    fontSize: 24,
    fontFamily: fonts.bold,
    color: colors.text,
    marginTop: 8,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    fontFamily: fonts.regular,
    color: colors.textLight,
    textAlign: "center",
  },
  // Section Header Styles
  attendeeSection: {
    flex: 1,
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontFamily: fonts.bold,
    color: colors.text,
  },
  attendeeCount: {
    backgroundColor: colors.primary,
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 6,
    minWidth: 32,
    alignItems: "center",
  },
  countText: {
    fontSize: 14,
    fontFamily: fonts.bold,
    color: colors.white,
  },
  // Attendee Card Styles
  attendeeCard: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
    borderLeftWidth: 4,
    borderLeftColor: colors.primary,
  },
  attendeeProfile: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: 16,
  },
  avatarContainer: {
    position: "relative",
  },
  attendeeAvatar: {
    width: 56,
    height: 56,
    borderRadius: 16,
    borderWidth: 2,
    borderColor: colors.primary + "20",
  },
  attendeePlaceholder: {
    width: 56,
    height: 56,
    borderRadius: 16,
    backgroundColor: colors.primary + "20",
    alignItems: "center",
    justifyContent: "center",
    borderWidth: 2,
    borderColor: colors.primary + "40",
  },
  placeholderText: {
    fontSize: 20,
    color: colors.primary,
    fontFamily: fonts.bold,
  },
  statusIndicator: {
    position: "absolute",
    bottom: -2,
    right: -2,
    width: 20,
    height: 20,
    borderRadius: 10,
    alignItems: "center",
    justifyContent: "center",
    borderWidth: 2,
    borderColor: colors.white,
  },
  attendeeInfo: {
    marginLeft: 16,
    flex: 1,
  },
  nameRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 4,
  },
  attendeeName: {
    fontSize: 18,
    fontFamily: fonts.bold,
    color: colors.text,
    flex: 1,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
    marginLeft: 8,
  },
  statusText: {
    fontSize: 12,
    fontFamily: fonts.bold,
  },
  username: {
    fontSize: 14,
    fontFamily: fonts.regular,
    color: colors.textLight,
  },
  timeDetails: {
    gap: 8,
  },
  timeRow: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  timeLabel: {
    fontSize: 14,
    fontFamily: fonts.regular,
    color: colors.textLight,
    flex: 1,
  },
  timeValue: {
    fontSize: 14,
    fontFamily: fonts.bold,
    color: colors.text,
  },
  // Empty State Styles
  emptyState: {
    alignItems: "center",
    justifyContent: "center",
    padding: 48,
    backgroundColor: colors.white,
    borderRadius: 16,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  emptyText: {
    fontSize: 20,
    fontFamily: fonts.bold,
    color: colors.textLight,
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 16,
    fontFamily: fonts.regular,
    color: colors.textLight,
    textAlign: "center",
    lineHeight: 24,
  },
  errorText: {
    fontSize: 16,
    fontFamily: fonts.regular,
    color: colors.textLight,
    textAlign: "center",
    marginTop: 32,
  },
});
