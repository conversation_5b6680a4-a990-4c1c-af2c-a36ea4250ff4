import { Button } from "@/components";
import HeaderBack from "@/components/header-back";
import LoadingSpinner from "@/components/loading-spinner";
import { useLocation } from "@/hooks/useLocation";
import { useSocket } from "@/hooks/useSocket";
import api from "@/lib/axios";
import { colors } from "@/utils/colors";
import { fonts } from "@/utils/fonts";
import { useQueryClient } from "@tanstack/react-query";
import { CameraView, useCameraPermissions } from "expo-camera";
import { router, useLocalSearchParams } from "expo-router";
import { useEffect, useRef, useState } from "react";
import { StyleSheet, Text, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import Toast from "react-native-toast-message";

interface BarcodeScanningResult {
  type: string;
  data: string;
}

export default function CheckIn() {
  const [permission, requestPermission] = useCameraPermissions();
  const [scanned, setScanned] = useState(false);
  const { id } = useLocalSearchParams();
  const { emit } = useSocket();
  const queryClient = useQueryClient();
  const cameraRef = useRef<CameraView>(null);
  const { location } = useLocation();

  useEffect(() => {
    if (permission && !permission.granted) {
      requestPermission();
    }
  }, [permission]);

  const handleBarcodeScanned = async (result: BarcodeScanningResult) => {
    const { data } = result;
    setScanned(true);
    try {
      if (!location) {
        Toast.show({
          type: "error",
          text1: "Location Required",
          text2: "Please wait for location to be detected",
        });
        return;
      }
      await api.post(`/attendance/check-in/${id}`, {
        token: data,
        longitude: location.coords.longitude,
        latitude: location.coords.latitude,
      });

      Toast.show({
        type: "success",
        text1: "Success",
        text2: "Successfully checked in",
      });
      queryClient.invalidateQueries({
        queryKey: ["attendance", id],
      });
      emit("check_in", {
        token: data,
        sessionId: id,
        longitude: location.coords.longitude,
        latitude: location.coords.latitude,
      });
      router.back();
    } catch (error: any) {
      Toast.show({
        type: "error",
        text1: "Error",
        text2: error.response?.data?.message || "Something went wrong",
      });

      console.log(error);
      setScanned(false);
    }
  };

  if (!permission) {
    // Camera permissions are still loading
    return <LoadingSpinner />;
  }

  if (!permission.granted) {
    // Camera permissions are not granted yet
    return (
      <SafeAreaView style={styles.container}>
        <HeaderBack />
        <View style={styles.content}>
          <Text style={styles.title}>No access to camera</Text>
          <Text style={styles.subtitle}>
            Please grant camera permission to use the QR scanner
          </Text>
          <Button
            title="Grant Permission"
            onPress={requestPermission}
            style={styles.button}
          />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <HeaderBack />
      <View style={styles.content}>
        <Text style={styles.title}>Scan QR Code</Text>
        <Text style={styles.subtitle}>
          Scan the QR code displayed by the session owner
        </Text>
        <View style={styles.scanner}>
          <CameraView
            ref={cameraRef}
            style={StyleSheet.absoluteFillObject}
            facing="back"
            onBarcodeScanned={scanned ? undefined : handleBarcodeScanned}
            barcodeScannerSettings={{
              barcodeTypes: ["qr"],
            }}
          />
        </View>
        {scanned && (
          <Button
            title="Tap to Scan Again"
            onPress={() => setScanned(false)}
            style={styles.button}
          />
        )}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
    padding: 16,
  },
  content: {
    flex: 1,
    padding: 16,
    alignItems: "center",
  },
  title: {
    fontSize: 24,
    fontFamily: fonts.bold,
    color: colors.text,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: fonts.regular,
    color: colors.textLight,
    textAlign: "center",
    marginBottom: 32,
  },
  scanner: {
    width: "100%",
    aspectRatio: 1,
    overflow: "hidden",
    borderRadius: 20,
  },
  button: {
    marginTop: 32,
  },
});
