import { Button } from "@/components";
import Badge from "@/components/badge";
import HeaderBack from "@/components/header-back";
import LoadingSpinner from "@/components/loading-spinner";
import { useSocket } from "@/hooks/useSocket";
import api from "@/lib/axios";
import { useAuthStore } from "@/store/auth";
import { SingleSessionResponse } from "@/types/organisation";
// import { SingleSessionResponse } from "@/types/session";
import { colors } from "@/utils/colors";
import { fonts } from "@/utils/fonts";
import { formatTime } from "@/utils/format-date";
import { useQuery } from "@tanstack/react-query";
import * as FileSystem from "expo-file-system";
import { router, useLocalSearchParams } from "expo-router";
import { useEffect, useState } from "react";
import {
  Image,
  Pressable,
  ScrollView,
  StyleSheet,
  Text,
  View,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
// import * as MediaLibrary from 'expo-media-library';
import Toast from "react-native-toast-message";

export default function SingleSession() {
  const { id } = useLocalSearchParams();
  const { user } = useAuthStore();
  const { on } = useSocket();
  const [attendance, setAttendance] = useState<any>([]);

  const { data, isLoading } = useQuery<SingleSessionResponse>({
    queryKey: ["session", id],
    queryFn: () => api.get(`/sessions/${id}`).then((res) => res.data),
  });

  useEffect(() => {
    if (data?.data.attendnace) {
      setAttendance(data.data.attendnace);
    }
  }, [data?.data.attendnace]);

  const {
    data: attendanceData,
    // isLoading: attendanceLoading,
    // error,
  } = useQuery({
    queryKey: ["attendance", id],
    queryFn: () =>
      api.get(`/attendance/session/user/${id}`).then((res) => res.data),
    enabled:
      !!user && user?.id.toString() !== data?.data.session.creatorId.toString(),
  });

  on("user_checked_in", (newAttendance) => {
    setAttendance((prev: any[]) => {
      const alreadyExists = prev.some((a) => a.userId === newAttendance.userId);
      if (alreadyExists) return prev;
      return [...prev, newAttendance];
    });
  });

  const checkInTime = attendanceData?.data.checkedInAt;
  const checkOutTime = attendanceData?.data.checkedOutAt;
  console.log("====================================");
  console.log(attendance);
  console.log("====================================");
  const downloadQRCode = async () => {
    try {
      // const { status } = await MediaLibrary.requestPermissionsAsync();
      // if (status !== 'granted') {
      //   Toast.show({
      //     type: 'error',
      //     text1: 'Permission denied',
      //     text2: 'Please grant permission to save QR code'
      //   });
      //   return;
      // }

      const qrCodeUri = data?.data.session.qrcode;
      if (!qrCodeUri) return;

      const fileName = `qrcode-${id}.png`;
      const fileUri = `${FileSystem.documentDirectory}${fileName}`;

      const downloadResult = await FileSystem.downloadAsync(qrCodeUri, fileUri);

      if (downloadResult.status === 200) {
        // await MediaLibrary.saveToLibraryAsync(downloadResult.uri);
        Toast.show({
          type: "success",
          text1: "Success",
          text2: "QR Code saved to gallery",
        });
      }
    } catch (error) {
      Toast.show({
        type: "error",
        text1: "Error",
        text2: "Failed to download QR code",
      });
    }
  };

  const isCreator = user?.id === data?.data.session.creatorId;
  const attendee = user?.id !== data?.data.session.creatorId;
  const isSessionExpired =
    new Date() > new Date(data?.data.session.stopTime || "");
  if (isLoading) {
    return <LoadingSpinner />;
  }

  return (
    <SafeAreaView style={styles.container}>
      <HeaderBack title="Session Details" />
      <ScrollView>
        <View style={styles.content}>
          <View style={styles.orgInfo}>
            {data?.data.session.organisationAvatar ? (
              <Image
                source={{ uri: data.data.session.organisationAvatar }}
                style={styles.orgAvatar}
              />
            ) : (
              <View style={styles.placeholderAvatar}>
                <Text style={styles.placeholderText}>
                  {data?.data.session.organisationName[0]}
                </Text>
              </View>
            )}

            <Text style={styles.orgName}>
              {data?.data.session.organisationName}
            </Text>

            <Text style={styles.description}>
              This is Attendance session for{" "}
              {data?.data.session.organisationName} between {""}{" "}
              {formatTime(data?.data.session.startTime as string)} {""}
              and {""} {formatTime(data?.data.session.stopTime as string)}
            </Text>
          </View>

          <View style={styles.details}>
            <View style={styles.badgeContainer}>
              <Badge
                text={`Stops: ${formatTime(
                  data?.data.session.stopTime as string
                )}`}
                variant="error"
              />
              <Badge
                text={`Starts: ${formatTime(
                  data?.data.session.startTime as string
                )}`}
                variant="success"
              />

              <Badge
                text={isSessionExpired ? "Ended" : "Ongoing"}
                variant={isSessionExpired ? "error" : "info"}
              />
            </View>

            {isSessionExpired && (
              <View style={styles.actions}>
                <Text style={styles.label}>Your Attendance</Text>
                <View
                  style={[
                    styles.badgeContainer,
                    { flexWrap: "wrap", width: "50%" },
                  ]}
                >
                  {checkInTime ? (
                    <Badge
                      text={`Checked In: ${formatTime(checkInTime as string)}`}
                    />
                  ) : (
                    <Badge text="Not Checked In" variant="error" />
                  )}
                  {checkOutTime ? (
                    <Badge
                      text={`Checked Out: ${formatTime(
                        checkOutTime as string
                      )}`}
                    />
                  ) : (
                    <Badge text="Not Checked Out" variant="error" />
                  )}
                </View>
              </View>
            )}

            {attendee && !isSessionExpired && (
              <View style={styles.actions}>
                <View style={{ flexWrap: "wrap", width: "50%", gap: 8 }}>
                  {checkInTime && (
                    <Badge
                      text={`Checked In: ${formatTime(checkInTime as string)}`}
                      variant="info"
                    />
                  )}
                  {checkOutTime && (
                    <Badge
                      text={`Checked Out: ${formatTime(
                        checkOutTime as string
                      )}`}
                      variant="error"
                    />
                  )}
                </View>
                {!checkInTime && (
                  <Button
                    title="Check in"
                    onPress={() => router.push(`/session/${id}/check-in`)}
                    style={styles.button}
                  />
                )}
                {!checkOutTime && checkInTime && (
                  <Button
                    title="Check out"
                    onPress={() => router.push(`/session/${id}/check-out`)}
                    style={styles.button}
                  />
                )}
              </View>
            )}

            {isCreator && !isSessionExpired && (
              <Pressable onPress={downloadQRCode} style={styles.qrContainer}>
                <Image
                  source={{ uri: data?.data.session.qrcode }}
                  style={styles.qrCode}
                  resizeMode="contain"
                />
                <Text style={styles.downloadText}>Tap to download QR Code</Text>
              </Pressable>
            )}

            {user?.id === data?.data.session.creatorId && (
              <View style={styles.attendeeSection}>
                <Text style={styles.label}>Attendees:</Text>
                {attendance?.length ? (
                  attendance.map((attendee: any, index: number) => (
                    <View key={index} style={{ gap: 8 }}>
                      <View style={styles.attendeeProfile}>
                        {attendee.avatar ? (
                          <Image
                            source={{ uri: attendee.avatar }}
                            style={styles.attendeeAvatar}
                          />
                        ) : (
                          <View style={styles.attendeePlaceholder}>
                            <Text style={styles.placeholderText}>
                              {attendee.firstName[0]}
                            </Text>
                          </View>
                        )}
                        <View>
                          <Text style={styles.attendeeName}>
                            {`${attendee.firstName} ${attendee.lastName}`}
                          </Text>
                          <Text style={styles.username}>
                            @{attendee.username}
                          </Text>
                        </View>
                      </View>

                      <View style={{ gap: 8, width: "70%" }}>
                        <Badge
                          text={`Checked In: ${formatTime(
                            attendee.checkedInAt
                          )}`}
                          variant="info"
                        />
                        {attendee.checkedOutAt && (
                          <>
                            <Badge
                              text={`Checked Out:${formatTime(
                                attendee.checkedOutAt
                              )}`}
                              variant="error"
                            />
                          </>
                        )}
                      </View>
                    </View>
                  ))
                ) : (
                  <Text style={styles.value}>No attendees yet</Text>
                )}
              </View>
            )}
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: colors.background,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  orgInfo: {
    alignItems: "center",
    marginBottom: 24,
  },
  badgeContainer: {
    flexDirection: "row",
    gap: 8,
    marginBottom: 16,
  },

  attendeeSection: {
    marginTop: 16,
  },

  attendeeItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 16,
    backgroundColor: colors.white,
    padding: 16,
    borderRadius: 12,
    shadowColor: "#ccc",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },

  description: {
    fontSize: 16,
    textAlign: "center",
    fontFamily: fonts.regular,
    color: colors.text,
    marginBottom: 16,
  },

  attendeeProfile: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
  },

  attendeeAvatar: {
    width: 50,
    height: 50,
    borderRadius: 12,
  },

  attendeePlaceholder: {
    width: 50,
    height: 50,
    borderRadius: 12,
    backgroundColor: colors.textLight,
    alignItems: "center",
    justifyContent: "center",
  },

  orgAvatar: {
    width: 100,
    height: 100,
    borderRadius: 30,
    marginBottom: 8,
  },

  attendeeName: {
    fontSize: 16,
    fontFamily: fonts.bold,
    color: colors.text,
  },

  username: {
    fontSize: 14,
    fontFamily: fonts.regular,
    color: colors.text,
  },

  attendeeTimes: {
    alignItems: "flex-end",
  },

  timeLabel: {
    fontSize: 14,
    fontFamily: fonts.bold,
    color: colors.textLight,
    marginBottom: 4,
  },

  timeValue: {
    fontSize: 16,
    fontFamily: fonts.regular,
    color: colors.text,
  },

  placeholderAvatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: colors.textLight,
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 8,
  },
  placeholderText: {
    fontSize: 32,
    color: colors.text,
    fontFamily: fonts.bold,
  },
  orgName: {
    fontSize: 24,
    fontFamily: fonts.bold,
    color: colors.text,
  },
  details: {
    backgroundColor: colors.white,
    padding: 16,
    borderRadius: 12,
  },
  label: {
    fontSize: 16,
    fontFamily: fonts.bold,
    color: colors.textLight,
    marginBottom: 4,
  },
  value: {
    fontSize: 18,
    fontFamily: fonts.regular,
    color: colors.text,
    marginBottom: 16,
  },
  actions: {
    marginTop: 16,
    gap: 12,
  },
  button: {
    width: "100%",
  },
  qrContainer: {
    alignItems: "center",
    justifyContent: "center",
    borderWidth: 1,
    borderColor: colors.text + "20",
    borderRadius: 12,
    marginTop: 16,
    padding: 16,
  },
  qrCode: {
    width: 200,
    height: 200,
  },
  downloadText: {
    marginTop: 8,
    fontSize: 14,
    fontFamily: fonts.regular,
    color: colors.textLight,
  },
});
