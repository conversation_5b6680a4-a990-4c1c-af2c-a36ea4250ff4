import { Button } from "@/components";
import FormLayout from "@/components/form-layout";
import HeaderBack from "@/components/header-back";
import { useLocation } from "@/hooks/useLocation";
import { useSocket } from "@/hooks/useSocket";
import api from "@/lib/axios";
import { OrganisationResponse } from "@/types/organisation";
import { colors } from "@/utils/colors";
import { fonts } from "@/utils/fonts";
import { Ionicons } from "@expo/vector-icons";
import DateTimePicker from "@react-native-community/datetimepicker";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { router } from "expo-router";
import { Formik } from "formik";
import { useState } from "react";
import {
  KeyboardAvoidingView,
  Modal,
  Platform,
  Pressable,
  ScrollView,
  StyleSheet,
  Text,
  View,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import Toast from "react-native-toast-message";
import * as yup from "yup";

const schema = yup.object().shape({
  organisationId: yup.number().required("Organization is required"),
  startTime: yup.date().required("Start time is required"),
  stopTime: yup
    .date()
    .required("End time is required")
    .test("is-valid-end-time", "End time must be valid", function (value) {
      if (!value) return false;
      return true;
    })
    .min(yup.ref("startTime"), "End time must be after start time"),
});

type SessionFormValues = {
  organisationId: string;
  startTime: Date;
  stopTime: Date;
  latitude: number;
  longitude: number;
};

const NativeDateTimePicker = ({
  value,
  onTimeChange,
}: {
  value: Date;
  onTimeChange: (date: Date) => void;
}) => {
  const [showPicker, setShowPicker] = useState(false);

  const onChange = (event: any, selectedDate?: Date) => {
    const currentDate = selectedDate || value;

    if (Platform.OS === "android") {
      setShowPicker(false);
    }

    onTimeChange(currentDate);
  };

  const showTimePicker = () => {
    setShowPicker(true);
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  return (
    <View style={styles.dateContainer}>
      <Pressable style={styles.orgButton} onPress={showTimePicker}>
        <Text style={styles.dateText}>{formatTime(value)}</Text>
      </Pressable>

      {showPicker && (
        <DateTimePicker
          value={value}
          mode="time"
          is24Hour={false}
          display={Platform.OS === "ios" ? "spinner" : "default"}
          onChange={onChange}
        />
      )}

      {Platform.OS === "ios" && showPicker && (
        <Pressable
          style={styles.doneButton}
          onPress={() => setShowPicker(false)}
        >
          <Text style={styles.doneText}>Done</Text>
        </Pressable>
      )}
    </View>
  );
};

export default function NewSession() {
  const { location, locationLoading } = useLocation();
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedOrg, setSelectedOrg] = useState<string | null>(null);
  const queryClient = useQueryClient();
  const { emit } = useSocket();

  const { data: organizations, isLoading: orgsLoading } =
    useQuery<OrganisationResponse>({
      queryKey: ["organisations"],
      queryFn: () => api.get("/organisations/user").then((res) => res.data),
    });

  const mutation = useMutation({
    mutationFn: (values: SessionFormValues) =>
      api.post("/sessions/create", values).then((res) => res.data),
    onSuccess: (data) => {
      console.log("data", data);
      Toast.show({
        type: "success",
        text1: "Success",
        text2: "Session created successfully",
      });
      queryClient.invalidateQueries({
        queryKey: ["ongoingSessions"],
      });
      emit("join_session", { sessionId: data.data.id });
      router.back();
    },
    onError: (error: any, variable) => {
      Toast.show({
        type: "error",
        text1: "Error",
        text2: error.response?.data?.message || "Something went wrong",
      });
    },
  });

  const now = new Date();
  const oneHourLater = new Date(now.getTime() + 60 * 60 * 1000);

  const submitSession = (values: SessionFormValues) => {
    if (!location) {
      Toast.show({
        type: "error",
        text1: "Location Required",
        text2: "Please wait for location to be detected",
      });
      return;
    }

    const submitValues = {
      ...values,
      latitude: location.coords.latitude,
      longitude: location.coords.longitude,
    };

    mutation.mutate(submitValues);
  };

  return (
    <SafeAreaView style={styles.container}>
      <HeaderBack />
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.keyboardView}
      >
        <Formik
          initialValues={{
            organisationId: selectedOrg || "",
            startTime: now,
            stopTime: oneHourLater,
            latitude: location?.coords.latitude || 0,
            longitude: location?.coords.longitude || 0,
          }}
          validationSchema={schema}
          onSubmit={submitSession}
          enableReinitialize={true}
        >
          {({ handleSubmit, setFieldValue, values, errors, touched }) => (
            <FormLayout
              title="New Session"
              subtitle="Create a new attendance session"
            >
              <View style={styles.form}>
                <View style={styles.pickerContainer}>
                  <Pressable
                    style={styles.orgButton}
                    onPress={() => setModalVisible(true)}
                  >
                    <Text style={styles.orgText}>
                      {values.organisationId
                        ? organizations?.data?.find(
                            (org) => org.id === +values.organisationId
                          )?.name
                        : "Select an organization"}
                    </Text>
                  </Pressable>

                  <Modal
                    visible={modalVisible}
                    animationType="slide"
                    transparent={true}
                    onRequestClose={() => setModalVisible(false)}
                  >
                    <View style={styles.modalContainer}>
                      <View style={styles.modalContent}>
                        <Text style={styles.modalTitle}>
                          Select Organization
                        </Text>
                        {orgsLoading ? (
                          <Text style={styles.loadingText}>
                            Loading organizations...
                          </Text>
                        ) : (
                          <ScrollView>
                            {organizations?.data?.map((org: any) => (
                              <Pressable
                                key={org.id}
                                style={styles.orgOption}
                                onPress={() => {
                                  setFieldValue("organisationId", org.id);
                                  setSelectedOrg(org.id);
                                  setModalVisible(false);
                                }}
                              >
                                <Text style={styles.orgOptionText}>
                                  {org.name}
                                </Text>
                              </Pressable>
                            ))}
                          </ScrollView>
                        )}
                        <Button
                          title="Close"
                          onPress={() => setModalVisible(false)}
                        />
                      </View>
                    </View>
                  </Modal>

                  {errors.organisationId && touched.organisationId && (
                    <Text style={styles.error}>{errors.organisationId}</Text>
                  )}
                </View>

                {/* Location Status */}
                <View style={styles.locationContainer}>
                  {locationLoading ? (
                    <Text style={styles.loadingText}>Getting location...</Text>
                  ) : location ? (
                    <View style={[styles.orgButton, styles.locationContainer]}>
                      <Ionicons
                        name="location-outline"
                        size={18}
                        color={colors.primary}
                      />
                      <Text style={styles.locationText}>
                        ({location.coords.latitude.toFixed(6)},{" "}
                        {location.coords.longitude.toFixed(6)})
                      </Text>
                    </View>
                  ) : (
                    <Text style={styles.errorText}>Location not available</Text>
                  )}
                </View>

                <>
                  <NativeDateTimePicker
                    value={values.startTime}
                    onTimeChange={(date) => setFieldValue("startTime", date)}
                  />
                  <NativeDateTimePicker
                    value={values.stopTime}
                    onTimeChange={(date) => setFieldValue("stopTime", date)}
                  />
                </>

                {errors.startTime && touched.startTime && (
                  <Text style={styles.error}>{errors.startTime as string}</Text>
                )}
                {errors.stopTime && touched.stopTime && (
                  <Text style={styles.error}>{errors.stopTime as string}</Text>
                )}

                <Button
                  title="Create Session"
                  onPress={handleSubmit}
                  disabled={mutation.isPending || locationLoading || !location}
                />
              </View>
            </FormLayout>
          )}
        </Formik>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
    padding: 16,
    paddingBottom: 100,
  },
  keyboardView: {
    flex: 1,
  },
  form: {
    gap: 16,
  },

  modalContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  modalContent: {
    backgroundColor: colors.white,
    borderRadius: 25,
    padding: 16,
    width: "80%",
    maxHeight: "80%",
  },
  modalTitle: {
    fontSize: 18,
    marginBottom: 16,
    fontFamily: fonts.bold,
    color: colors.text,
  },
  orgOption: {
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.text + "10",
  },
  orgOptionText: {
    fontSize: 16,
    color: colors.text,
  },
  pickerContainer: {
    gap: 8,
  },
  dateContainer: {
    gap: 8,
  },
  locationContainer: {
    gap: 4,
    flexDirection: "row",
    alignItems: "center",
  },
  label: {
    fontSize: 16,
    fontWeight: "600",
    color: colors.text,
  },
  orgButton: {
    width: "100%",
    height: 52,
    backgroundColor: colors.white,
    borderRadius: 20,
    shadowColor: "#ccc",
    padding: 8,
    shadowOffset: {
      width: 0,
      height: 9,
    },
    shadowOpacity: 0.29,
    elevation: 20,
    flexDirection: "row",
    alignItems: "center",
  },
  selectedOrg: {
    borderColor: colors.primary,
    backgroundColor: colors.primary + "10",
  },
  orgText: {
    fontSize: 16,
    color: colors.text,
  },
  selectedOrgText: {
    color: colors.primary,
    fontWeight: "600",
  },
  dateButton: {
    padding: 12,
    borderRadius: 8,
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.text + "30",
  },
  dateText: {
    fontSize: 16,
    color: colors.text,
  },
  doneButton: {
    backgroundColor: colors.primary,
    padding: 10,
    borderRadius: 6,
    alignItems: "center",
    marginTop: 8,
  },
  doneText: {
    color: "white",
    fontWeight: "600",
  },
  error: {
    color: "red",
    fontSize: 12,
  },
  loadingText: {
    fontSize: 14,
    color: colors.text + "80",
    fontStyle: "italic",
  },
  locationText: {
    fontSize: 20,
    fontFamily: fonts.bold,
    color: colors.primary,
  },
  errorText: {
    fontSize: 14,
    color: colors.danger,
  },
});
