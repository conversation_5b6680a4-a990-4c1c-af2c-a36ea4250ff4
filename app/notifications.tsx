import HeaderBack from "@/components/header-back";
import NotificationList from "@/components/notification-list";
import { StyleSheet } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
const Notifications = () => {
  return (
    <SafeAreaView style={styles.container}>
      <HeaderBack title="Notifications" />
      <NotificationList />
    </SafeAreaView>
  );
};
export default Notifications;
const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
});
