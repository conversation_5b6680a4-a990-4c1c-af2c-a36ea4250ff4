import { Button, Input } from "@/components";
import FormLayout from "@/components/form-layout";
import HeaderBack from "@/components/header-back";
import api from "@/lib/axios";
import { colors } from "@/utils/colors";
import { useMutation } from "@tanstack/react-query";
import { router } from "expo-router";
import { Formik } from "formik";
import { KeyboardAvoidingView, Platform, StyleSheet } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import Toast from "react-native-toast-message";
import * as yup from "yup";

const schema = yup.object().shape({
  code: yup.string().required("Organisation code is required"),
});

type JoinOrgFormValues = {
  code: string;
};

export default function JoinOrg() {
  const joinMutation = useMutation({
    mutationFn: (values: JoinOrgFormValues) =>
      api.post("/members/join", values).then((res) => res.data),
    onSuccess: () => {
      Toast.show({
        type: "success",
        text1: "Success",
        text2: "Successfully joined organisation",
      });
      router.back();
    },
    onError: (error: any) => {
      Toast.show({
        type: "error",
        text1: "Error",
        text2: error.response?.data?.message || "Something went wrong",
      });
    },
  });

  const handleSubmit = (values: JoinOrgFormValues) => {
    joinMutation.mutate(values);
  };

  return (
    <SafeAreaView style={styles.container}>
      <HeaderBack title="Join Organisation" />
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.keyboardView}
      >
        <Formik
          initialValues={{ code: "" }}
          validationSchema={schema}
          onSubmit={handleSubmit}
        >
          {({ handleChange, handleBlur, handleSubmit, values, errors }) => (
            <FormLayout
              title="Join Organisation"
              subtitle="Enter organisation code to join"
            >
              <>
                <Input
                  placeholder="Enter organisation code"
                  value={values.code}
                  onChangeText={handleChange("code")}
                  onBlur={() => handleBlur("code")}
                  error={errors.code}
                  icon="key"
                />
                <Button
                  title="Join"
                  onPress={handleSubmit}
                  disabled={joinMutation.isPending}
                />
              </>
            </FormLayout>
          )}
        </Formik>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: colors.background,
  },
  keyboardView: {
    flex: 1,
  },
});
