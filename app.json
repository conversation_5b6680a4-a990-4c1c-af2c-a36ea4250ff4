{"expo": {"name": "presentt", "slug": "presentt", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/logo.png", "scheme": "presentt", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "bundleIdentifier": "com.charles22.presentt", "infoPlist": {"ITSAppUsesNonExemptEncryption": false}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/logo.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "package": "com.charles22.presentt", "googleServicesFile": "./google-services.json"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/presentt.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}]], "experiments": {"typedRoutes": true}, "extra": {"router": {}, "eas": {"projectId": "4e3a6a15-2006-443d-a4c8-cf3af8bd327a"}}, "owner": "charles22"}}