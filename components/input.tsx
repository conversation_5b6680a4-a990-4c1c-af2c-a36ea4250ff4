import { colors } from "@/utils/colors";
import { fonts } from "@/utils/fonts";
import { Ionicons } from "@expo/vector-icons";
import { useState } from "react";
import { Pressable, StyleSheet, Text, TextInput, View } from "react-native";

interface InputProps {
  label?: string;
  placeholder?: string;
  value?: string;
  multiline?: boolean;
  onBlur?: () => void;
  onChangeText?: (text: string) => void;
  error?: string;
  secureTextEntry?: boolean;
  icon?: keyof typeof Ionicons.glyphMap;
  editable?: boolean;
}

export default function Input({
  label,
  placeholder,
  value,
  onBlur,
  editable = true,
  onChangeText,
  error,
  secureTextEntry,
  icon,
  multiline,
}: InputProps) {
  const [showPassword, setShowPassword] = useState(false);

  return (
    <View style={styles.container}>
      {label && <Text style={styles.label}>{label}</Text>}
      <View style={[styles.inputContainer, error && styles.inputError]}>
        {icon && (
          <Ionicons
            name={icon}
            size={20}
            color={colors.text}
            style={styles.leftIcon}
          />
        )}
        <TextInput
          style={[styles.input, icon && styles.inputWithIcon]}
          placeholder={placeholder}
          placeholderTextColor={colors.text}
          value={value}
          onChangeText={onChangeText}
          onBlur={onBlur}
          editable={editable}
          secureTextEntry={secureTextEntry && !showPassword}
          multiline={multiline}
        />
        {secureTextEntry && (
          <Pressable
            onPress={() => setShowPassword(!showPassword)}
            style={styles.rightIcon}
          >
            <Ionicons
              name={showPassword ? "eye-off-outline" : "eye-outline"}
              size={20}
              color={colors.text}
            />
          </Pressable>
        )}
      </View>
      {error && <Text style={styles.error}>**{error}</Text>}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: "100%",
    marginBottom: 16,
    borderRadius: 20,
  },
  label: {
    fontSize: 14,
    marginBottom: 8,
    fontFamily: fonts.regular,
    color: colors.text,
  },
  inputContainer: {
    width: "100%",
    height: 52,
    backgroundColor: colors.white,
    borderRadius: 20,
    shadowColor: "#ccc",
    shadowOffset: {
      width: 0,
      height: 9,
    },
    shadowOpacity: 0.29,
    elevation: 20,
    flexDirection: "row",
    alignItems: "center",
  },
  input: {
    flex: 1,
    height: "100%",
    paddingHorizontal: 16,
    borderRadius: 30,
    fontSize: 16,
    fontFamily: fonts.regular,
    color: colors.text,
  },
  inputWithIcon: {
    paddingLeft: 12,
  },
  inputError: {
    borderColor: "#ff3b30",
  },
  error: {
    color: "#ff3b30",
    fontSize: 12,
    marginTop: 4,
    marginLeft: 6,
    fontFamily: fonts.bold,
  },
  leftIcon: {
    marginLeft: 12,
  },
  rightIcon: {
    padding: 8,
    marginRight: 4,
  },
});
