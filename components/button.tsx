import { colors } from "@/utils/colors";
import { fonts } from "@/utils/fonts";
import { Pressable, StyleSheet, Text, ViewStyle } from "react-native";

interface ButtonProps {
  title: string;
  onPress: () => void;
  style?: ViewStyle;
  variant?: "primary" | "secondary" | "outline";
  disabled?: boolean;
}

export default function Button({
  title,
  onPress,
  style,
  variant = "primary",
  disabled = false,
}: ButtonProps) {
  return (
    <Pressable
      style={({ pressed }) => [
        styles.container,
        styles[variant],
        style,
        pressed && styles.pressed,
        disabled && styles.disabled,
      ]}
      onPress={onPress}
      disabled={disabled}
    >
      <Text style={[styles.title, variant === "outline" && styles.outlineText]}>
        {title}
      </Text>
    </Pressable>
  );
}

const styles = StyleSheet.create({
  container: {
    width: "100%",
    height: 52,
    borderRadius: 20,
    elevation: 20,
    shadowColor: "#222",
    shadowOffset: {
      width: 0,
      height: 20,
    },
    shadowOpacity: 0.29,
    justifyContent: "center",
    alignItems: "center",
  },
  primary: {
    backgroundColor: colors.primary,
  },
  secondary: {
    backgroundColor: colors.secondary,
  },
  outline: {
    backgroundColor: "transparent",
    borderWidth: 1,
    borderColor: colors.primary,
  },
  pressed: {
    opacity: 0.8,
  },
  disabled: {
    opacity: 0.5,
  },
  title: {
    color: colors.white,
    fontSize: 16,
    fontFamily: fonts.bold,
  },
  outlineText: {
    color: colors.primary,
  },
});
