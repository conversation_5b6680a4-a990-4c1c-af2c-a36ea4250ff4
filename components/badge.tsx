import { colors } from "@/utils/colors";
import { fonts } from "@/utils/fonts";
import { StyleSheet, Text, View, ViewStyle } from "react-native";

interface BadgeProps {
  text: string;
  variant?: "success" | "error" | "warning" | "info";
  style?: ViewStyle;
}

export default function Badge({ text, variant = "info", style }: BadgeProps) {
  return (
    <View style={[styles.container, styles[variant], style]}>
      <Text style={[styles.text, styles[`${variant}Text`]]}>{text}</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 10,
  },
  success: {
    backgroundColor: "#E8F5E9",
  },
  error: {
    backgroundColor: "#FFEBEE",
  },
  warning: {
    backgroundColor: "#FFF3E0",
  },
  info: {
    backgroundColor: "#E3F2FD",
  },
  text: {
    fontSize: 12,
    fontFamily: fonts.bold,
  },
  successText: {
    color: colors.primary,
  },
  errorText: {
    color: "#C62828",
  },
  warningText: {
    color: "#EF6C00",
  },
  infoText: {
    color: "#1565C0",
  },
});
