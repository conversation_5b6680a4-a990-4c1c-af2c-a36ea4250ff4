import { colors } from "@/utils/colors";
import { fonts } from "@/utils/fonts";
import { AntDesign } from "@expo/vector-icons";
import { router } from "expo-router";
import { Pressable, StyleSheet, Text, View } from "react-native";

interface HeaderBackProps {
  title?: string;
}

export default function HeaderBack({ title }: HeaderBackProps) {
  return (
    <View style={styles.container}>
      <Pressable onPress={() => router.back()} style={styles.arrow}>
        <AntDesign name="left" size={20} color={colors.secondary} />
      </Pressable>
      {title && <Text style={styles.title}>{title}</Text>}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: "100%",
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  arrow: {
    width: 32,
    height: 32,
    backgroundColor: colors.white,
    borderRadius: 12,
    shadowColor: "#ccc",
    shadowOffset: {
      width: 0,
      height: 20,
    },
    shadowOpacity: 0.29,
    elevation: 20,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 16,
  },
  title: {
    fontSize: 20,
    fontFamily: fonts.bold,
    color: colors.secondary,
  },
});
