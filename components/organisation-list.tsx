import api from "@/lib/axios";
import { Organisation } from "@/types/organisation";
import { colors } from "@/utils/colors";
import { fonts } from "@/utils/fonts";
import { Ionicons } from "@expo/vector-icons"; // Add this import for the copy icon
import { useQuery } from "@tanstack/react-query";
import * as Clipboard from "expo-clipboard";
import { router } from "expo-router";
import {
  FlatList,
  Image,
  Pressable,
  StyleSheet,
  Text,
  ToastAndroid,
  View,
} from "react-native";
import Badge from "./badge";
import LoadingSpinner from "./loading-spinner";

export default function OrganisationList() {
  const { data, isLoading, error, refetch } = useQuery({
    queryKey: ["organisations"],
    queryFn: () => api.get("/organisations/user").then((res) => res.data),
  });

  const copyCodeToClipboard = async (code: string) => {
    await Clipboard.setStringAsync(code);
    ToastAndroid.show("Code copied to clipboard!", ToastAndroid.SHORT);
  };

  if (isLoading) {
    return <LoadingSpinner />;
  }

  if (error) {
    return <Badge variant="error" text="Error fetching organisations" />;
  }

  const organisations = data?.data as Organisation[];
  return (
    <FlatList
      data={organisations}
      keyExtractor={(item) => item.id.toString()}
      showsVerticalScrollIndicator={false}
      refreshing={isLoading}
      onRefresh={() => {
        refetch();
      }}
      contentContainerStyle={styles.container}
      ItemSeparatorComponent={() => <View style={styles.separator} />}
      renderItem={({ item }) => (
        <Pressable onPress={() => router.push(`/org/${item.id}/single`)}>
          <View style={styles.cardContainer}>
            <View style={styles.itemContainer}>
              <View style={styles.imageContainer}>
                {item.avatar ? (
                  <Image
                    source={{ uri: item.avatar }}
                    style={{ width: "100%", height: "100%", borderRadius: 12 }}
                  />
                ) : (
                  <View
                    style={{
                      width: "100%",
                      height: "100%",
                      backgroundColor: colors.textLight,
                      borderRadius: 12,
                    }}
                  />
                )}
              </View>
              <View style={styles.textContainer}>
                <Text style={styles.name}>{item.name}</Text>
                <Text style={styles.description}>
                  {item.description.slice(0, 50)}......
                </Text>
              </View>
              <View style={styles.codeContainer}>
                <Text style={styles.code}>{item.code}</Text>
                <Pressable
                  style={styles.copyButton}
                  onPress={() => copyCodeToClipboard(item.code)}
                >
                  <Ionicons
                    name="copy-outline"
                    size={18}
                    color={colors.primary}
                  />
                </Pressable>
              </View>
            </View>
          </View>
        </Pressable>
      )}
    />
  );
}

const styles = StyleSheet.create({
  container: {
    // paddingVertical: 16,
    // paddingHorizontal: 16,
  },
  cardContainer: {
    backgroundColor: colors.white,
    borderRadius: 20,
    shadowColor: "#ccc",
    padding: 8,
    shadowOffset: {
      width: 0,
      height: 20,
    },
    shadowOpacity: 0.29,
    elevation: 20,
  },
  separator: {
    height: 16,
  },
  imageContainer: {
    width: 50,
    height: 50,
    backgroundColor: colors.textLight,
    borderRadius: 12,
  },
  itemContainer: {
    flexDirection: "row",
    padding: 12,
    alignItems: "center",
  },
  textContainer: {
    flex: 1,
    marginLeft: 12,
  },
  codeContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: colors.textLight + "20", // Adding transparency
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  copyButton: {
    marginLeft: 8,
    padding: 4,
  },
  name: {
    fontSize: 16,
    fontFamily: fonts.bold,
    color: colors.secondary,
    marginBottom: 2,
  },
  description: {
    fontSize: 14,
    fontFamily: "pt_regular",
    color: "#22272A",
  },
  code: {
    fontSize: 12,
    fontFamily: "pt_regular",
    color: colors.primary,
  },
});
