import api from "@/lib/axios";
import { SessionResponse } from "@/types/session";
import { colors } from "@/utils/colors";
import { fonts } from "@/utils/fonts";
import { useQuery } from "@tanstack/react-query";
import { FlatList, Image, StyleSheet, Text, View } from "react-native";
import Badge from "./badge";
import LoadingSpinner from "./loading-spinner";

export default function SessionList() {
  const { data, isLoading, error, refetch } = useQuery<SessionResponse>({
    queryKey: ["userSessions"],
    queryFn: () => api.get("/sessions/user").then((res) => res.data),
  });

  const getSessionStatus = (startTime: string, endTime: string) => {
    const now = new Date().getTime();
    const start = new Date(startTime).getTime();
    const end = new Date(endTime).getTime();

    if (now < start) return { text: "Upcoming", variant: "warning" };
    if (now > end) return { text: "Ended", variant: "error" };
    return { text: "Ongoing", variant: "success" };
  };
  const formatTime = (time: string) => new Date(time).toLocaleTimeString();

  if (isLoading) {
    return <LoadingSpinner />;
  }

  return (
    <FlatList
      data={data?.data}
      keyExtractor={(item) => item.id.toString()}
      showsVerticalScrollIndicator={false}
      refreshing={isLoading}
      onRefresh={() => {
        refetch();
      }}
      renderItem={({ item }) => (
        <View style={styles.sessionItem}>
          <View style={styles.statusBadge}>
            <Badge
              text={getSessionStatus(item.startTime, item.stopTime).text}
              //   variant={getSessionStatus(item.startTime, item.stopTime).variant}
            />
          </View>
          <View style={styles.orgInfo}>
            {item.organisationAvatar ? (
              <Image
                source={{ uri: item.organisationAvatar }}
                style={styles.orgAvatar}
              />
            ) : (
              <View style={styles.placeholderAvatar}>
                <Text style={styles.placeholderText}>
                  {item.organisationName[0]}
                </Text>
              </View>
            )}
            <View style={styles.sessionDetails}>
              <Text style={styles.orgName}>{item.organisationName}</Text>
              <Text style={styles.sessionDate}>
                {new Date(item.createdAt).toLocaleDateString("en-US", {
                  weekday: "long",
                  year: "numeric",
                  month: "long",
                  day: "numeric",
                })}
              </Text>
              <View style={styles.badgeContainer}>
                <Badge
                  text={`Start: ${formatTime(item.startTime)}`}
                  variant="success"
                />
                <Badge
                  text={`End: ${formatTime(item.stopTime)}`}
                  variant="error"
                />
              </View>
            </View>
          </View>
        </View>
      )}
    />
  );
}

const styles = StyleSheet.create({
  sessionItem: {
    backgroundColor: colors.white,
    borderRadius: 20,
    padding: 16,
    marginBottom: 16,
    shadowColor: "#ccc",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    position: "relative",
  },
  statusBadge: {
    position: "absolute",
    top: 8,
    right: 8,
    zIndex: 1,
  },
  orgInfo: {
    flexDirection: "row",
    alignItems: "center",
  },
  orgAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  placeholderAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: colors.textLight,
    alignItems: "center",
    justifyContent: "center",
  },
  placeholderText: {
    fontSize: 24,
    color: colors.text,
    fontFamily: fonts.bold,
  },
  sessionDetails: {
    marginLeft: 12,
    flex: 1,
  },
  orgName: {
    fontSize: 16,
    color: colors.text,
    fontFamily: fonts.bold,
    marginBottom: 4,
  },
  sessionDate: {
    fontSize: 14,
    color: colors.text,
    fontFamily: fonts.regular,
    opacity: 0.7,
    marginBottom: 8,
  },
  badgeContainer: {
    flexDirection: "row",
    gap: 8,
  },
  startBadge: {
    backgroundColor: "#E8F5E9",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  endBadge: {
    backgroundColor: "#FFEBEE",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  badgeText: {
    fontSize: 12,
    fontFamily: fonts.bold,
    color: colors.text,
  },
});
