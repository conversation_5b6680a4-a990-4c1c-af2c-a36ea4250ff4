import { colors } from "@/utils/colors";
import { fonts } from "@/utils/fonts";
import React from "react";
import Toast, { BaseToast, ErrorToast } from "react-native-toast-message";

const toastConfig = {
  success: (props: any) => (
    <BaseToast
      {...props}
      style={{
        borderLeftColor: "#059669",
        backgroundColor: "#D1FAE5",
        borderRadius: 20,
        shadowColor: "#059669",
        shadowOffset: {
          width: 0,
          height: 20,
        },
        shadowOpacity: 0.29,
        elevation: 20,
      }}
      contentContainerStyle={{ paddingHorizontal: 15 }}
      text1Style={{
        fontSize: 16,
        fontFamily: fonts.regular,

        color: colors.secondary,
      }}
      text2Style={{
        fontSize: 14,
        fontFamily: fonts.regular,
        color: colors.text,
      }}
    />
  ),
  error: (props: any) => (
    <ErrorToast
      {...props}
      style={{
        borderLeftColor: "#DC2626",
        backgroundColor: "#FEE2E2",
        borderRadius: 20,
        shadowColor: "#DC2626",
        shadowOffset: {
          width: 0,
          height: 20,
        },
        shadowOpacity: 0.29,
        elevation: 20,
      }}
      text1Style={{
        fontSize: 16,
        fontFamily: fonts.regular,
        color: colors.text,
      }}
      text2Style={{
        fontSize: 14,
        fontFamily: fonts.regular,
        color: colors.text,
      }}
    />
  ),
};

export default function ToastProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <>
      {children}
      <Toast config={toastConfig} />
    </>
  );
}
