import * as Notifications from "expo-notifications";
import { createContext, useEffect, useState } from "react";

export const NotificationContext = createContext<{
  expoPushToken: string;
  notification: Notifications.Notification | undefined;
}>({
  expoPushToken: "",
  notification: undefined,
});
export default function NotificationContextProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const [expoPushToken, setExpoPushToken] = useState("");
  const [notification, setNotification] = useState<
    Notifications.Notification | undefined
  >(undefined);

  useEffect(() => {
    //    registerForPushNotificationsAsync()
    //      .then((token) => setExpoPushToken(token ?? ""))
    //      .catch((error: any) => setExpoPushToken(`${error}`));

    const notificationListener = Notifications.addNotificationReceivedListener(
      (notification) => {
        setNotification(notification);
      }
    );

    const responseListener =
      Notifications.addNotificationResponseReceivedListener((response) => {
        console.log(response);
      });

    return () => {
      notificationListener.remove();
      responseListener.remove();
    };
  }, []);
  return (
    <NotificationContext.Provider value={{ expoPushToken, notification }}>
      {children}
    </NotificationContext.Provider>
  );
}
