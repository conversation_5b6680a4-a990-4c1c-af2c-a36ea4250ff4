import { colors } from "@/utils/colors";
import { fonts } from "@/utils/fonts";
import { Ionicons } from "@expo/vector-icons";
import { Pressable, StyleSheet, Text, ViewStyle } from "react-native";

interface ArrowButtonProps {
  title: string;
  onPress: () => void;
  style?: ViewStyle;
}

export default function ArrowButton({
  title,
  onPress,
  style,
}: ArrowButtonProps) {
  return (
    <Pressable
      style={({ pressed }) => [
        styles.container,
        style,
        pressed && styles.pressed,
      ]}
      onPress={onPress}
    >
      <Text style={styles.title}>{title}</Text>
      <Ionicons name="arrow-forward" size={20} color={colors.white} />
    </Pressable>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    gap: 8,
    padding: 12,
    paddingHorizontal: 32,
    borderRadius: 20,
    backgroundColor: colors.primary,
    shadowColor: "#ccc",
    shadowOffset: {
      width: 0,
      height: 20,
    },
    shadowOpacity: 0.29,
    elevation: 20,
  },
  pressed: {
    opacity: 0.8,
  },
  title: {
    color: colors.white,
    fontSize: 16,
    fontFamily: fonts.bold,
  },
});
