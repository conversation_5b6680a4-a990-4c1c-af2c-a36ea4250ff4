import api from "@/lib/axios";
import { useQuery } from "@tanstack/react-query";
import React, { useState } from "react";
import { FlatList, Text, View } from "react-native";
import Badge from "./badge";
import LoadingSpinner from "./loading-spinner";

export default function NotificationList() {
  const [page, setPage] = useState(1);
  const limit = 10;
  const offset = (page - 1) * limit;

  const { data, isLoading, error } = useQuery({
    queryKey: ["notifications", offset, limit],
    queryFn: () =>
      api
        .get(`/notifications?limit=${limit}&page=${offset}`)
        .then((res) => res.data),
  });

  const loadMore = () => {
    setPage((prev) => prev + 1);
  };

  if (isLoading) return <LoadingSpinner />;
  if (error)
    return <Badge variant="error" text="Error fetching notifications" />;

  console.log("====================================");
  console.log(data);
  console.log("====================================");
  return (
    <FlatList
      data={data?.data || []}
      renderItem={() => (
        <View>
          <Text>Hello</Text>
        </View>
      )}
      onEndReached={loadMore}
      onEndReachedThreshold={0.5}
    />
  );
}
