import api from "@/lib/axios";
import { ProfileResponse } from "@/types/profile";
import { colors } from "@/utils/colors";
import { fonts } from "@/utils/fonts";
import { Ionicons } from "@expo/vector-icons";
import { useQuery } from "@tanstack/react-query";
import { router } from "expo-router";
import { Image, Pressable, StyleSheet, Text, View } from "react-native";

const MainHeader = () => {
  const { data, isLoading } = useQuery<ProfileResponse>({
    queryKey: ["profile"],
    queryFn: async () => api.get("/profile").then((res) => res.data),
  });

  return (
    <View style={styles.container}>
      <Pressable
        onPress={() => router.push("/notifications")}
        style={styles.notificationContainer}
      >
        <Ionicons
          size={30}
          name="notifications-outline"
          color={colors.secondary}
        />
        <View style={styles.badge}>
          <Text style={styles.badgeText}>3</Text>
        </View>
      </Pressable>

      <Pressable
        onPress={() => router.push("/profile")}
        style={styles.rightContainer}
      >
        <View style={styles.userInfo}>
          <Text style={styles.welcomeText}>
            Hello, {data?.data.firstName || "User"}
          </Text>
          <View style={styles.verifiedContainer}>
            <Ionicons name="checkmark-circle" size={16} color="#007AFF" />
            <Text style={styles.verifiedText}>Verified</Text>
          </View>
        </View>
        <View style={styles.imageContainer}>
          <Image
            style={styles.image}
            source={{
              uri: data?.data.avatar || "https://via.placeholder.com/40",
            }}
          />
        </View>
      </Pressable>
    </View>
  );
};

export default MainHeader;

const styles = StyleSheet.create({
  container: {
    paddingTop: 50,
    paddingHorizontal: 16,
    flexDirection: "row",
    // backgroundColor: colors.background,
    justifyContent: "space-between",
    alignItems: "center",
  },
  notificationContainer: {
    position: "relative",
  },
  badge: {
    position: "absolute",
    top: -5,
    right: -5,
    backgroundColor: "#FF3B30",
    borderRadius: 7,
    minWidth: 20,
    height: 20,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 2,
    borderColor: colors.white,
  },
  badgeText: {
    color: "#FFF",
    fontSize: 12,
    fontFamily: fonts.regular,
    fontWeight: "bold",
  },
  rightContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
  },
  userInfo: {
    alignItems: "flex-end",
    gap: 2,
  },
  imageContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    overflow: "hidden",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  image: {
    height: "100%",
    width: "100%",
    resizeMode: "cover",
  },
  welcomeText: {
    fontFamily: fonts.regular,
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
  },
  verifiedContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
    backgroundColor: "#F0F8FF",
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: "#007AFF20",
  },
  verifiedText: {
    fontSize: 12,
    color: "#007AFF",
    fontFamily: fonts.regular,
    fontWeight: "500",
  },
});
