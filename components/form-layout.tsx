import { colors } from "@/utils/colors";
import { fonts } from "@/utils/fonts";
import { router } from "expo-router";
import { StyleSheet, Text, View } from "react-native";

interface FormLayoutProps {
  children: React.ReactNode;
  title: string;
  subtitle?: string;

  footerText?: string;
  footerLink?: any;
  footerLinkText?: string;
}

export default function FormLayout({
  children,
  title,
  subtitle,
  footerText,
  footerLink,
  footerLinkText,
}: FormLayoutProps) {
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>{title}</Text>
        <Text style={styles.subtitle}>{subtitle}</Text>
      </View>

      {children}

      <View style={styles.footer}>
        <Text style={styles.footerText}>{footerText}</Text>
        <Text
          onPress={() => footerLink && router.push(footerLink)}
          style={styles.footerLink}
        >
          {footerLinkText}
        </Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: "100%",
  },

  header: {
    marginBottom: 32,
  },
  title: {
    fontSize: 40,
    fontFamily: fonts.bold,
    textAlign: "center",
    color: colors.secondary,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: colors.text,
    textAlign: "center",
    fontFamily: fonts.regular,
  },
  footer: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    marginTop: 16,
  },
  footerText: {
    fontSize: 16,
    color: colors.secondary,
    fontFamily: fonts.regular,
  },
  footerLink: {
    fontSize: 14,
    color: colors.primary,
    marginLeft: 4,
    fontFamily: fonts.bold,
  },
});
