import { useSocket } from "@/hooks/useSocket";
import api from "@/lib/axios";
import { OngoingSessionsResponse } from "@/types/session";
import { colors } from "@/utils/colors";
import { fonts } from "@/utils/fonts";
import { useQuery } from "@tanstack/react-query";
import { router } from "expo-router";
import { useEffect, useState } from "react";
import {
  FlatList,
  Image,
  Pressable,
  StyleSheet,
  Text,
  View,
} from "react-native";
import LoadingSpinner from "./loading-spinner";

export default function OngoingSessions() {
  const { emit } = useSocket();
  const { data, isLoading, refetch } = useQuery<OngoingSessionsResponse>({
    queryKey: ["ongoingSessions"],
    queryFn: () => api.get("/sessions/ongoing").then((res) => res.data),
    // refetchInterval: 1000,
  });

  const [countdowns, setCountdowns] = useState<{ [key: number]: string }>({});

  useEffect(() => {
    const timer = setInterval(() => {
      if (data?.data) {
        const newCountdowns: { [key: number]: string } = {};
        data.data.forEach((session) => {
          const now = new Date().getTime();
          const end = new Date(session.stop_time).getTime();
          const timeLeft = end - now;

          if (timeLeft > 0) {
            const hours = Math.floor(timeLeft / (1000 * 60 * 60));
            const minutes = Math.floor(
              (timeLeft % (1000 * 60 * 60)) / (1000 * 60)
            );
            const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);
            newCountdowns[session.id] = `${hours}h ${minutes}m ${seconds}s`;
          } else {
            newCountdowns[session.id] = "Ended";
          }
        });
        setCountdowns(newCountdowns);
      }
    }, 1000);

    return () => clearInterval(timer);
  }, [data]);

  if (isLoading) {
    return <LoadingSpinner />;
  }

  return (
    // <View style={styles.container}>
    <FlatList
      contentContainerStyle={{ flex: 1 }}
      data={data?.data}
      keyExtractor={(item) => item.id.toString()}
      showsVerticalScrollIndicator={false}
      refreshing={isLoading}
      onRefresh={() => {
        refetch();
      }}
      renderItem={({ item }) => (
        <Pressable
          onPress={() => router.push(`/session/${item.id}/single`)}
          onPressIn={() => emit("join_session", { sessionId: item.id })}
          style={styles.sessionItem}
        >
          <View style={styles.orgInfo}>
            {item.avatar ? (
              <Image source={{ uri: item.avatar }} style={styles.orgAvatar} />
            ) : (
              <View style={styles.placeholderAvatar}>
                <Text style={styles.placeholderText}>{item.name[0]}</Text>
              </View>
            )}
            <View style={styles.sessionDetails}>
              <Text style={styles.orgName}>{item.name}</Text>
              <Text style={styles.countdown}>{countdowns[item.id]}</Text>
            </View>
          </View>
        </Pressable>
      )}
    />
    // </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
    padding: 16,
  },
  sessionItem: {
    backgroundColor: colors.white,
    borderRadius: 20,
    padding: 16,
    marginBottom: 16,
    shadowColor: "#ccc",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  orgInfo: {
    flexDirection: "row",
    alignItems: "center",
  },
  orgAvatar: {
    width: 50,
    height: 50,
    borderRadius: 12,
  },
  placeholderAvatar: {
    width: 50,
    height: 50,
    borderRadius: 12,
    backgroundColor: colors.textLight,
    alignItems: "center",
    justifyContent: "center",
  },
  placeholderText: {
    fontSize: 24,
    color: colors.text,
    fontFamily: fonts.bold,
  },
  sessionDetails: {
    marginLeft: 12,
    flex: 1,
  },
  orgName: {
    fontSize: 16,
    color: colors.text,
    fontFamily: fonts.bold,
    marginBottom: 4,
  },
  countdown: {
    fontSize: 14,
    color: colors.textLight,
    fontFamily: fonts.regular,
  },
});
