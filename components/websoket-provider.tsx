import { useAuthStore } from "@/store/auth";
import { SocketContextType } from "@/types/socket";
import React, {
  createContext,
  useCallback,
  useEffect,
  useRef,
  useState,
} from "react";
import { AppState, AppStateStatus } from "react-native";
import Toast from "react-native-toast-message";
import { io, Socket } from "socket.io-client";

export const SocketContext = createContext<SocketContextType | null>(null);

export default function WebSocketProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const { user } = useAuthStore();

  // State
  const [socket, setSocket] = useState<Socket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [connectionStatus, setConnectionStatus] =
    useState<SocketContextType["connectionStatus"]>("disconnected");
  const [reconnectAttempt, setReconnectAttempt] = useState(0);

  // Refs
  const socketRef = useRef<Socket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isManualDisconnect = useRef(false);
  const eventListenersRef = useRef<Map<string, ((...args: any[]) => void)[]>>(
    new Map()
  );

  // Socket configuration
  const SOCKET_URL = "https://present-backend-no5j.onrender.com";
  const SOCKET_OPTIONS = {
    transports: ["websocket"],
  };

  const connect = useCallback(() => {
    if (!user?.token) {
      console.log("No auth token available, skipping socket connection");
      return;
    }

    if (socketRef.current?.connected) {
      console.log("Socket already connected");
      return;
    }

    try {
      console.log("Connecting to WebSocket server...");
      setConnectionStatus("connecting");
      isManualDisconnect.current = false;

      const socketInstance = io(SOCKET_URL, {
        ...SOCKET_OPTIONS,
        auth: {
          token: user.token,
        },
      });

      socketRef.current = socketInstance;
      setSocket(socketInstance);

      // Connection event handlers
      socketInstance.on("connect", () => {
        console.log(
          "Connected to WebSocket server with ID:",
          socketInstance.id
        );
        setIsConnected(true);
        setConnectionStatus("connected");
        setReconnectAttempt(0);

        // Clear any pending reconnect timeouts
        if (reconnectTimeoutRef.current) {
          clearTimeout(reconnectTimeoutRef.current);
          reconnectTimeoutRef.current = null;
        }
      });

      socketInstance.on("disconnect", (reason) => {
        setIsConnected(false);
        setConnectionStatus("disconnected");

        // Only attempt to reconnect if it wasn't a manual disconnect
        if (!isManualDisconnect.current && reason !== "io client disconnect") {
          setConnectionStatus("reconnecting");
        }
      });

      socketInstance.on("connect_error", (error) => {
        Toast.show({
          type: "error",
          text1: "Connection Error",
          text2: "Failed to connect to server",
        });
        setConnectionStatus("error");
        setIsConnected(false);
      });

      socketInstance.on("reconnect", (attemptNumber) => {
        console.log(
          "Successfully reconnected after",
          attemptNumber,
          "attempts"
        );
        setReconnectAttempt(0);
        setConnectionStatus("connected");
      });

      socketInstance.on("reconnect_attempt", (attemptNumber) => {
        console.log("Reconnection attempt:", attemptNumber);
        setReconnectAttempt(attemptNumber);
        setConnectionStatus("reconnecting");
      });

      socketInstance.on("reconnect_failed", () => {
        Toast.show({
          type: "error",
          text1: "Connection Error",
          text2: "Failed to reconnect to server",
        });
        setConnectionStatus("error");
        setReconnectAttempt(0);
      });

      // Authentication error handling
      socketInstance.on("auth_error", (error) => {
        Toast.show({
          type: "error",
          text1: "Authentication Error",
          text2: "Please login again",
        });
        setConnectionStatus("error");
        // You might want to trigger a logout here
        // logout();
      });
    } catch (error) {
      console.error("Failed to create socket connection:", error);
      setConnectionStatus("error");
    }
  }, [user?.token]);

  // Disconnect function
  const disconnect = useCallback(() => {
    console.log("Manually disconnecting socket...");
    isManualDisconnect.current = true;

    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    if (socketRef.current) {
      socketRef.current.disconnect();
      socketRef.current = null;
    }

    setSocket(null);
    setIsConnected(false);
    setConnectionStatus("disconnected");
    setReconnectAttempt(0);
  }, []);

  // Emit function with connection check
  const emit = useCallback((event: string, data?: any): boolean => {
    if (socketRef.current?.connected) {
      socketRef.current.emit(event, data);
      console.log("Emitting event:", event, "with data:", data);
      return true;
    } else {
      console.warn(`Cannot emit '${event}': Socket not connected`);
      return false;
    }
  }, []);

  // Enhanced event listener functions
  const on = useCallback(
    (event: string, listener: (...args: any[]) => void) => {
      if (!socketRef.current) {
        console.warn(
          `Cannot add listener for '${event}': Socket not initialized`
        );
        return;
      }

      socketRef.current.on(event, listener);

      // Store listener reference for cleanup
      if (!eventListenersRef.current.has(event)) {
        eventListenersRef.current.set(event, []);
      }
      eventListenersRef.current.get(event)!.push(listener);
    },
    []
  );

  const off = useCallback(
    (event: string, listener?: (...args: any[]) => void) => {
      if (!socketRef.current) return;

      if (listener) {
        socketRef.current.off(event, listener);

        // Remove from stored listeners
        const listeners = eventListenersRef.current.get(event);
        if (listeners) {
          const index = listeners.indexOf(listener);
          if (index > -1) {
            listeners.splice(index, 1);
          }
          if (listeners.length === 0) {
            eventListenersRef.current.delete(event);
          }
        }
      } else {
        socketRef.current.off(event);
        eventListenersRef.current.delete(event);
      }
    },
    []
  );

  // Handle app state changes (important for mobile)
  useEffect(() => {
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (
        nextAppState === "active" &&
        !isConnected &&
        user?.token &&
        !isManualDisconnect.current
      ) {
        console.log("App became active, attempting to reconnect...");
        connect();
      } else if (nextAppState === "background") {
        console.log("App went to background");
        // Optionally disconnect to save battery
        // disconnect();
      }
    };

    const subscription = AppState.addEventListener(
      "change",
      handleAppStateChange
    );
    return () => subscription?.remove();
  }, [isConnected, user?.token, connect]);

  // Main effect for handling auth state changes
  useEffect(() => {
    if (user?.token) {
      connect();
    } else {
      // User logged out, disconnect socket
      disconnect();
    }

    return () => {
      // Cleanup on unmount
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }

      // Remove all event listeners
      eventListenersRef.current.forEach((listeners, event) => {
        listeners.forEach((listener) => {
          socketRef.current?.off(event, listener);
        });
      });
      eventListenersRef.current.clear();

      if (socketRef.current) {
        socketRef.current.disconnect();
        socketRef.current = null;
      }
    };
  }, [user?.token, connect, disconnect]);

  // Context value
  const contextValue: SocketContextType = {
    socket,
    isConnected,
    connectionStatus,
    reconnectAttempt,
    emit,
    on,
    off,
    connect,
    disconnect,
  };

  return (
    <SocketContext.Provider value={contextValue}>
      {children}
    </SocketContext.Provider>
  );
}
