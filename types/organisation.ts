export interface OrganisationResponse {
  data: Organisation[];
  message: string;
  status: number;
}

export interface Organisation {
  avatar: null;
  code: string;
  createdAt: string;
  createdBy: number;
  description: string;
  id: number;
  name: string;
  updatedAt: string;
}

export interface SingleOrgResponse {
  data: Data;
  message: string;
  status: number;
}

interface Data {
  id: number;
  name: string;
  description: string;
  code: string;
  createdBy: number;
  avatar: string | null;
  createdAt: string;
  updatedAt: string;
  creator: Creator;
  members: Member[];
}

interface Member {
  id: number;
  username: string;
  email: string;
  avatar: string;
  joinedAt: string;
}

interface Creator {
  id: number;
  username: string;
  email: string;
}
export interface SingleSessionResponse {
  data: Data;
  message: string;
  status: number;
}

interface Data {
  attendnace: any[];
  session: Session;
}

interface Session {
  createdAt: string;
  creatorId: number;
  id: number;
  latitude: string;
  longitude: string;
  organisationAvatar: string;
  organisationId: number;
  organisationName: string;
  qrcode: string;
  startTime: string;
  stopTime: string;
  token: string;
  updatedAt: string;
}
