export interface SessionResponse {
  data: Datum[];
  message: string;
  status: number;
}

interface Datum {
  createdAt: string;
  id: number;
  latitude: string;
  startTime: string;
  longitude: string;
  stopTime: string;
  organisationId: number;
  qrcode: string;
  token: string;
  organisationAvatar: string;
  organisationName: string;
  updatedAt: string;
}

interface RootObject {
  data: Datum[];
  message: string;
  status: number;
}

// interface Datum {
//   organisation_members: function[];
//   sessions: function[];
// }

export interface OngoingSessionsResponse {
  data: OngoingSession[];
  message: string;
  status: number;
}

interface OngoingSession {
  id: number;
  organisation_id: number;
  longitude: string;
  latitude: string;
  token: string;
  qrcode: string;
  name: string;
  avatar: null | string;
  start_time: string;
  stop_time: string;
  created_at: string;
  updated_at: string;
}
